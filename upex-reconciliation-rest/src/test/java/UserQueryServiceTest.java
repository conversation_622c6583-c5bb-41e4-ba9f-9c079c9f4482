import com.upex.reconciliation.rest.ReconciliationRestApplication;
import com.upex.reconciliation.service.business.UserQueryService;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReconciliationRestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class UserQueryServiceTest {

    @Resource
    private UserQueryService userQueryService;

    @Test
    public void queryUserSingleRealTimeAssets() {
        GlobalBillConfig config = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Pair<Boolean, List<Long>> result =  userQueryService.getChildListByParentId(2874456342L, config.getChildAccountTypeList(), 50, config.getMaxChildAccountCount());
        System.out.println(result);
        result =  userQueryService.getChildListByParentId(2874456342L, config.getChildAccountTypeList(), 40, 100);
        System.out.println(result);
        result =  userQueryService.getChildListByParentId(2874456342L, config.getChildAccountTypeList(), 30, 110);
        System.out.println(result);
        result =  userQueryService.getChildListByParentId(2874456342L, config.getChildAccountTypeList(), 100, 110);
        System.out.println(result);
        result =  userQueryService.getChildListByParentId(2874456342L, config.getChildAccountTypeList(), 30, 180);
        System.out.println(result);
    }
}
