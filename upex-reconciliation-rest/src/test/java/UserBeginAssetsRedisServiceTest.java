import com.upex.reconciliation.service.business.UserBeginAssetsRedisService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/5/12
 */
public class UserBeginAssetsRedisServiceTest extends BaseTest {

    @Resource
    private UserBeginAssetsRedisService userBeginAssetsRedisService;

    @Test
    public void testAddUserBeginAssets() {
        Long timestamp = 1745982600000L;
        userBeginAssetsRedisService.addUserBeginAssets(1028433967L, timestamp);
        userBeginAssetsRedisService.addUserBeginAssets(4457602581L, timestamp);
        userBeginAssetsRedisService.addUserBeginAssets(5558508288L, timestamp);
    }
}
