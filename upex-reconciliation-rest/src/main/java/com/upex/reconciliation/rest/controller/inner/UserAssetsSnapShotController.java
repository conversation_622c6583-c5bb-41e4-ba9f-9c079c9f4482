package com.upex.reconciliation.rest.controller.inner;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.dto.results.*;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import com.upex.reconciliation.facade.params.*;
import com.upex.reconciliation.service.business.UserAssetsSnapShotBiz;
import com.upex.reconciliation.service.business.ReconUserAssetsSnapShotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/inner/v1/recon")
@Slf4j
public class UserAssetsSnapShotController {

    @Resource
    private UserAssetsSnapShotBiz userAssetsSnapShotBiz;

    @Resource
    private ReconUserAssetsSnapShotService reconUserAssetsSnapShotService;

    /**
     * 根据用户当前时间查询不同业务系统用户资产快照
     *
     * @param reconUserAssetsSnapShotParams
     * @return
     */
    @RequestMapping(value = "/selectCurrentSysAssetsSnapShotByTime")
    @ResponseBody
    public ReconBillMessagePage<ReconTotalBalanceVo> selectCurrentSysAssetsSnapShotByTime(@RequestBody ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams) {
        // 币币、otc 查询bill_user 拿最后check_ok_time. 再查询bill_coin_user_property_
        ReconBillMessagePage<ReconTotalBalanceVo> totalBalanceVoList = userAssetsSnapShotBiz.selectCurrentSysAssetsSnapShotByTime(reconUserAssetsSnapShotParams);
        return totalBalanceVoList;
    }


    /**
     * 查询用户出入金
     *
     * @param reconUserTypeChangeParams
     * @return
     */
    @RequestMapping(value = "/selectOutInBalance")
    @ResponseBody
    public List<ReconUserOutInBalanceVo> selectOutInBalance(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams) {
        // 先查A点快照、再查B点快照，基于两个时间点快照做差值
        List<ReconUserOutInBalanceVo> totalBalanceVoList = userAssetsSnapShotBiz.selectOutInBalance(reconUserTypeChangeParams);
        return totalBalanceVoList;
    }

    /**
     * 用户出入金详情
     *
     * @param reconUserTypeChangeParams
     * @return
     */
    @RequestMapping(value = "/selectDetails")
    @ResponseBody
    public ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> selectDetails(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams) {
        // 先查A点快照、再查B点快照，基于两个时间点快照做差值
        ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> reconBillMessagePage = userAssetsSnapShotBiz.selectDetails(reconUserTypeChangeParams);
        return reconBillMessagePage;
    }

    /**
     * 其他收入
     *
     * @param reconUserTypeChangeParams
     * @return
     */
    @RequestMapping(value = "/selectOtherIncome")
    @ResponseBody
    public List<ReconUserOutInBalanceDetailsVo> selectOtherIncome(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams) {
        // 先查A点快照、再查B点快照，基于两个时间点快照做差值
        List<ReconUserOutInBalanceDetailsVo> list = userAssetsSnapShotBiz.selectOtherIncome(reconUserTypeChangeParams);
        return list;
    }

    /**
     * 最后快照时间
     *
     * @param reconUserTypeChangeParams
     * @return
     */
    @RequestMapping(value = "/selectFinalSnapShotTime")
    @ResponseBody
    public String selectFinalSnapShotTime(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams) {
        log.info("selectFinalSnapShotTime userTypeChangeParams = {}", reconUserTypeChangeParams);
        String finalSnapShotTime = userAssetsSnapShotBiz.selectFinalSnapShotTime(reconUserTypeChangeParams);
        return finalSnapShotTime;
    }


    /**
     * 查询用户当前账户情况各系统资产总和
     *
     * @param reconUserAssetsSnapShotParams
     * @return
     */
    @RequestMapping(value = "/selectUserAssets")
    @ResponseBody
    public ReconUserAssetsVo selectUserAssets(@RequestBody ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams) {
        log.info("selectUserAssets userTypeChangeParams = {}", reconUserAssetsSnapShotParams);
        ReconUserAssetsVo list = userAssetsSnapShotBiz.selectUserAssets(reconUserAssetsSnapShotParams);
        return list;
    }


    /**
     * 查询币币系统交易盈亏情况
     *
     * @param reconUserTypeChangeParams
     * @return
     */
    @RequestMapping(value = "/selectBbTrading")
    @ResponseBody
    public List<ReconBbTradingVo> selectBbTrading(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams) {
        List<ReconBbTradingVo> list = userAssetsSnapShotBiz.selectBbTrading(reconUserTypeChangeParams);
        return list;
    }


    /**
     * 用户交易盈亏情况盈利额总额
     *
     * @param reconUserTypeChangeParams
     * @return
     */
    @RequestMapping(value = "/selectUserProfitLossTotal")
    @ResponseBody
    public ReconUserTradingVo selectUserProfitLossTotal(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams) {
        ReconUserTradingVo reconUserTradingVo = userAssetsSnapShotBiz.selectUserProfitLossTotal(reconUserTypeChangeParams);
        return reconUserTradingVo;
    }



    /**
     * 获取用户特定时间的资产详情
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/getUserAssetsBySnapShotTime")
    @ResponseBody
    public ReconTotalAssetsDetailVo getUserAssetsBySnapShotTime(@RequestBody ReconUserAssetsBySnapShotTimeParams params) {
        return reconUserAssetsSnapShotService.getUserAssetsBySnapShotTime(params);
    }


    /**
     * 批量获取用户特定时间的资产详情
     *
     * @param param 查询参数
     * @return
     */
    @PostMapping(value = "/batchUserAssetsBySnapShotTime")
    @ResponseBody
    public Map<Long, ReconTotalAssetsDetailVo> batchUserAssetsBySnapShotTime(@RequestBody ReconBillBaseBatchUserAssetsParams param) {
        List<Long> userIds = param.getUserIdList();
        Date snapshotTime = param.getSnapshotTime();
        if (CollectionUtils.isEmpty(userIds) || snapshotTime == null) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL, "账户参数用户id为空或快照时间为空");
        }
        return userAssetsSnapShotBiz.batchUserAssetsBySnapShotTime(userIds, snapshotTime.getTime(), Boolean.TRUE);
    }

    /**
     * 获取用户特定时间的资产详情
     *
     * @param reconUserAssetsParam 用户资产查询参数
     * @return {@link ReconUserAssetsResult }
     * <AUTHOR>
     * @date 2022/12/13 09:17
     */
    @PostMapping(value = "/getUserAssetsByCoinAndTime")
    public List<ReconUserAssetsResult> getUserAssetsByCoinAndTime(@RequestBody ReconUserAssetsParam reconUserAssetsParam) {
        if (reconUserAssetsParam.getUserId() == null) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL);
        }
        return userAssetsSnapShotBiz.getUserAssetsByCoinAndTime(reconUserAssetsParam);
    }



    /**
     * 查询u合约和币本位合约盈亏
     * @param userTypeChangeParams
     * @return
     */
    @RequestMapping(value = "/selectContract")
    @ResponseBody
    public List<ReconContractTradingVo> selectContract(@RequestBody ReconUserTypeChangeParams userTypeChangeParams) {
        List<ReconContractTradingVo> list = userAssetsSnapShotBiz.selectContract(userTypeChangeParams);
        return list;
    }


    /**
     * 获取用户指定时间的资产快照列表
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/getUserAssetsSnapShotList")
    @ResponseBody
    public List<UserAssetsDetailVo> getUserAssetsSnapShotList(@RequestBody UserAssetsSnapShotParams params) {
        params.validate();
        return reconUserAssetsSnapShotService.getUserAssetsSnapShotList(params);
    }

}
