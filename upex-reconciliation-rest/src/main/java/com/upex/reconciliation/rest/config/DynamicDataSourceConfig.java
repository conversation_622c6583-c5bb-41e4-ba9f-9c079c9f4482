package com.upex.reconciliation.rest.config;

import com.upex.commons.datasource.DataSourceInvoker;
import com.upex.commons.datasource.DynamicDataSource;
import com.upex.commons.datasource.config.DataSourceApolloConfigure;
import com.upex.commons.datasource.config.DataSourceConfigProperties;
import com.upex.reconciliation.service.common.DynamicJdbcTemplate;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;

/**
 * 数据源配置类
 *
 * <AUTHOR>
 * @date 2019/05/05
 **/
@Configuration
@MapperScan(basePackages = DynamicDataSourceConfig.PACKAGE, sqlSessionFactoryRef = "billSqlSessionFactory")
@EnableConfigurationProperties(value = {DataSourceConfigProperties.class})
public class DynamicDataSourceConfig {
    static final String PACKAGE = "com.upex.reconciliation.service.dao.mapper";
    private static final String MAPPER_LOCATION = "classpath*:mybatis/mapper/*.xml";

    @Bean("dynamicDataSource")
    public DynamicDataSource dynamicDataSource(final DataSourceConfigProperties configProperties, DataSourceApolloConfigure dataSourceApolloConfigure) {
        final DynamicDataSource dynamicDataSource = new DynamicDataSource(dataSourceApolloConfigure, configProperties);
        return dynamicDataSource;
    }

    @Bean("dataSourceInvoker")
    public DataSourceInvoker dataSourceInvoker(final DataSourceApolloConfigure dataSourceApolloConfigure) {
        DataSourceInvoker invoker = new DataSourceInvoker();
        return invoker;
    }

    @Bean(name = "dataSourceTransactionManager")
    public DataSourceTransactionManager dataSourceTransactionManager(@Qualifier("dynamicDataSource") DynamicDataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean("repeatReadTransactionTemplate")
    public TransactionTemplate getRepeatReadTransactionTemplate(@Qualifier("dataSourceTransactionManager") DataSourceTransactionManager transactionManager) {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_REPEATABLE_READ);
        return transactionTemplate;
    }

    @Bean("defaultTransactionTemplate")
    public TransactionTemplate getDefaultTransactionTemplate(@Qualifier("dataSourceTransactionManager") DataSourceTransactionManager transactionManager) {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        return transactionTemplate;
    }

    @Bean(name = "dynamicJdbcTemplate")
    public DynamicJdbcTemplate dynamicJdbcTemplate(@Qualifier("dynamicDataSource") DataSource dataSource) {
        return new DynamicJdbcTemplate(dataSource);
    }


    @Bean(name = "billSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("dynamicDataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean sessionFactory = AbstractDataSourceConfig.createSqlSessionFactoryBean(dataSource,
                DynamicDataSourceConfig.MAPPER_LOCATION);
        return sessionFactory.getObject();
    }

    @Bean(name = "billSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("billSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws ClassNotFoundException {
        return AbstractDataSourceConfig.createSqlSessionTemplate(sqlSessionFactory);
    }



}
