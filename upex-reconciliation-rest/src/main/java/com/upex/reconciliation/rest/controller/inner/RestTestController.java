package com.upex.reconciliation.rest.controller.inner;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "/v1/reconciliation/rest/test")
@Slf4j
public class RestTestController {

    @RequestMapping(value = "/health", method = RequestMethod.GET)
    @ResponseBody
    public String health() {
        return "success";
    }



    @RequestMapping(value = "/checkHealth", method = RequestMethod.GET)
    @ResponseBody
    public String checkHealth() {
        return "checkHealth for rest success";
    }

}


