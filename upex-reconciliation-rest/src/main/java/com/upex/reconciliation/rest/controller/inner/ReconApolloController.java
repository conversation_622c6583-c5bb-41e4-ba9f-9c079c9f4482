package com.upex.reconciliation.rest.controller.inner;

import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/inner/v1/recon/apollo/controller")
public class ReconApolloController {

    /**
     * 获取apollo配置
     * @param apolloKeyEnum 配置key枚举
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/12/10 19:56
     */
    @PostMapping("/getApolloConfig")
    public String getApolloConfig(@RequestBody ApolloKeyEnum apolloKeyEnum) {
        return ReconciliationApolloConfigUtils.getApolloStringConfig(apolloKeyEnum);
    }

}


