2025-05-20 16:53:42.729   INFO     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : upex-reconciliation-job is starting
2025-05-20 16:53:42.735   INFO     1 --- [                main] c.u.u.u.EnvUtils                         : EnvUtils env systemEnv = test6 envType=TEST
2025-05-20 16:53:42.743   INFO     1 --- [                main] c.u.u.i.WorkIdClient                     : getWorkId domain:http://upex-gateway.test6.bitget.tools spring.profiles.active:test6
2025-05-20 16:53:43.518   INFO     1 --- [                main] c.u.u.i.WorkIdClient                     : getWorkId request={"appName":"upex-bill-rest-test","bindedName":"************","clientTime":1747731222739,"env":"test","hostType":"VM","isGlobleId":true} result={"group":null,"code":"200","msg":null,"params":null,"data":{"id":489,"appName":"GlobalApp","bindedName":"************","status":"free","hostType":"VM","workId":426,"createTime":"2023-02-10T16:00:52.000+00:00","updateTime":"2025-05-20T08:52:26.000+00:00","latestRenewTime":"2025-05-20T08:52:26.000+00:00","serviceName":null}}
2025-05-20 16:53:43.526   INFO     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : SpotBillBizTypeEnum.BILL_MATCH_FEE_SYS_IN: BILL_MATCH_FEE_SYS_IN
2025-05-20 16:53:43.742   INFO    29 --- [  background-preinit] o.h.v.i.u.Version                        : HV000001: Hibernate Validator 6.1.7.Final
2025-05-20 16:53:43.832   INFO    25 --- [     pool-2-thread-1] c.u.u.i.WorkIdClient                     : renewWorkId primaryId=489,workId=426,result=
2025-05-20 16:53:43.999   WARN     1 --- [                main] c.c.f.f.i.p.DefaultApplicationProvider   : spring.application.name property is null
2025-05-20 16:53:43.999   INFO     1 --- [                main] c.c.f.f.i.p.DefaultApplicationProvider   : App ID is set to upex-reconciliation by app.id property from System Property
2025-05-20 16:53:43.999   WARN     1 --- [                main] c.c.f.f.i.p.DefaultApplicationProvider   : app.label is not available from System Property and /META-INF/app.properties. It is set to null
2025-05-20 16:53:44.000   INFO     1 --- [                main] c.c.f.f.i.p.DefaultServerProvider        : Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-05-20 16:53:44.015   WARN     1 --- [                main] c.c.f.a.i.DefaultMetaServerProvider      : Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties
2025-05-20 16:53:44.016   WARN     1 --- [                main] c.c.f.a.c.MetaDomainConsts               : Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders
2025-05-20 16:53:48.102   WARN     1 --- [                main] c.c.f.a.i.AbstractConfigRepository       : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:53:50.113   WARN     1 --- [                main] c.c.f.a.i.LocalFileConfigRepository      : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:53:50.123   INFO     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : The following profiles are active: test6
2025-05-20 16:53:51.236  ERROR     1 --- [                main] o.s.b.SpringApplication                  : Application run failed
java.lang.IllegalStateException: Service id not legal hostname (${upex.feignClient.uta.rest})
	at org.springframework.util.Assert.state(Assert.java:76) ~[spring-core-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:109) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:292) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:280) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerFeignClient(FeignClientsRegistrar.java:221) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerFeignClients(FeignClientsRegistrar.java:209) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerBeanDefinitions(FeignClientsRegistrar.java:150) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.context.annotation.ImportBeanDefinitionRegistrar.registerBeanDefinitions(ImportBeanDefinitionRegistrar.java:86) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.lambda$loadBeanDefinitionsFromRegistrars$1(ConfigurationClassBeanDefinitionReader.java:384) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsFromRegistrars(ConfigurationClassBeanDefinitionReader.java:383) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:148) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:332) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:237) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:280) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:96) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:707) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:533) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at com.upex.reconciliation.job.ReconciliationJobApplication.main(ReconciliationJobApplication.java:26) ~[classes/:?]
2025-05-20 16:53:51.236  ERROR     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : ReconciliationJobApplication Unexpected exception
java.lang.IllegalStateException: Service id not legal hostname (${upex.feignClient.uta.rest})
	at org.springframework.util.Assert.state(Assert.java:76) ~[spring-core-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:109) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:292) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:280) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerFeignClient(FeignClientsRegistrar.java:221) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerFeignClients(FeignClientsRegistrar.java:209) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerBeanDefinitions(FeignClientsRegistrar.java:150) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.context.annotation.ImportBeanDefinitionRegistrar.registerBeanDefinitions(ImportBeanDefinitionRegistrar.java:86) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.lambda$loadBeanDefinitionsFromRegistrars$1(ConfigurationClassBeanDefinitionReader.java:384) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsFromRegistrars(ConfigurationClassBeanDefinitionReader.java:383) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:148) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:332) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:237) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:280) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:96) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:707) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:533) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at com.upex.reconciliation.job.ReconciliationJobApplication.main(ReconciliationJobApplication.java:26) ~[classes/:?]
2025-05-20 16:53:52.130   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 1 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:53:53.621   INFO    25 --- [     pool-2-thread-1] c.u.u.i.WorkIdClient                     : renewWorkId primaryId=489,workId=426,result=
2025-05-20 16:53:55.140   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 2 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:53:59.160   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 4 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:54:03.787   INFO    25 --- [     pool-2-thread-1] c.u.u.i.WorkIdClient                     : renewWorkId primaryId=489,workId=426,result=
2025-05-20 16:54:05.179   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 8 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:54:13.623   INFO    25 --- [     pool-2-thread-1] c.u.u.i.WorkIdClient                     : renewWorkId primaryId=489,workId=426,result=
2025-05-20 16:54:15.197   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 16 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:54:23.803   INFO    25 --- [     pool-2-thread-1] c.u.u.i.WorkIdClient                     : renewWorkId primaryId=489,workId=426,result=
2025-05-20 16:55:13.909   INFO     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : upex-reconciliation-job is starting
2025-05-20 16:55:13.911   INFO     1 --- [                main] c.u.u.u.EnvUtils                         : EnvUtils env systemEnv = test6 envType=TEST
2025-05-20 16:55:13.917   INFO     1 --- [                main] c.u.u.i.WorkIdClient                     : getWorkId domain:http://upex-gateway.test6.bitget.tools spring.profiles.active:test6
2025-05-20 16:55:15.330   INFO     1 --- [                main] c.u.u.i.WorkIdClient                     : getWorkId request={"appName":"upex-bill-rest-test","bindedName":"************","clientTime":1747731313914,"env":"test","hostType":"VM","isGlobleId":true} result={"group":null,"code":"200","msg":null,"params":null,"data":{"id":489,"appName":"GlobalApp","bindedName":"************","status":"free","hostType":"VM","workId":426,"createTime":"2023-02-10T16:00:52.000+00:00","updateTime":"2025-05-20T08:54:24.000+00:00","latestRenewTime":"2025-05-20T08:54:24.000+00:00","serviceName":null}}
2025-05-20 16:55:15.341   INFO     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : SpotBillBizTypeEnum.BILL_MATCH_FEE_SYS_IN: BILL_MATCH_FEE_SYS_IN
2025-05-20 16:55:15.560   INFO    30 --- [  background-preinit] o.h.v.i.u.Version                        : HV000001: Hibernate Validator 6.1.7.Final
2025-05-20 16:55:15.857   WARN     1 --- [                main] c.c.f.f.i.p.DefaultApplicationProvider   : spring.application.name property is null
2025-05-20 16:55:15.857   INFO     1 --- [                main] c.c.f.f.i.p.DefaultApplicationProvider   : App ID is set to upex-reconciliation by app.id property from System Property
2025-05-20 16:55:15.857   WARN     1 --- [                main] c.c.f.f.i.p.DefaultApplicationProvider   : app.label is not available from System Property and /META-INF/app.properties. It is set to null
2025-05-20 16:55:15.858   INFO     1 --- [                main] c.c.f.f.i.p.DefaultServerProvider        : Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-05-20 16:55:15.873   WARN     1 --- [                main] c.c.f.a.i.DefaultMetaServerProvider      : Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties
2025-05-20 16:55:15.875   WARN     1 --- [                main] c.c.f.a.c.MetaDomainConsts               : Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders
2025-05-20 16:55:19.909   WARN     1 --- [                main] c.c.f.a.i.AbstractConfigRepository       : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:55:21.918   WARN     1 --- [                main] c.c.f.a.i.LocalFileConfigRepository      : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:55:21.932   INFO     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : The following profiles are active: test6
2025-05-20 16:55:23.193  ERROR     1 --- [                main] o.s.b.SpringApplication                  : Application run failed
java.lang.IllegalStateException: Service id not legal hostname (${upex.feignClient.uta.rest})
	at org.springframework.util.Assert.state(Assert.java:76) ~[spring-core-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:109) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:292) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:280) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerFeignClient(FeignClientsRegistrar.java:221) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerFeignClients(FeignClientsRegistrar.java:209) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerBeanDefinitions(FeignClientsRegistrar.java:150) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.context.annotation.ImportBeanDefinitionRegistrar.registerBeanDefinitions(ImportBeanDefinitionRegistrar.java:86) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.lambda$loadBeanDefinitionsFromRegistrars$1(ConfigurationClassBeanDefinitionReader.java:384) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsFromRegistrars(ConfigurationClassBeanDefinitionReader.java:383) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:148) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:332) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:237) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:280) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:96) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:707) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:533) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at com.upex.reconciliation.job.ReconciliationJobApplication.main(ReconciliationJobApplication.java:26) ~[classes/:?]
2025-05-20 16:55:23.194  ERROR     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : ReconciliationJobApplication Unexpected exception
java.lang.IllegalStateException: Service id not legal hostname (${upex.feignClient.uta.rest})
	at org.springframework.util.Assert.state(Assert.java:76) ~[spring-core-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:109) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:292) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.getName(FeignClientsRegistrar.java:280) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerFeignClient(FeignClientsRegistrar.java:221) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerFeignClients(FeignClientsRegistrar.java:209) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.registerBeanDefinitions(FeignClientsRegistrar.java:150) ~[spring-cloud-openfeign-core-2.2.9.RELEASE.jar:2.2.9.RELEASE]
	at org.springframework.context.annotation.ImportBeanDefinitionRegistrar.registerBeanDefinitions(ImportBeanDefinitionRegistrar.java:86) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.lambda$loadBeanDefinitionsFromRegistrars$1(ConfigurationClassBeanDefinitionReader.java:384) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsFromRegistrars(ConfigurationClassBeanDefinitionReader.java:383) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:148) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:332) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:237) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:280) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:96) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:707) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:533) ~[spring-context-5.2.14.RELEASE.jar:5.2.14.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236) ~[spring-boot-2.3.10.RELEASE.jar:2.3.10.RELEASE]
	at com.upex.reconciliation.job.ReconciliationJobApplication.main(ReconciliationJobApplication.java:26) ~[classes/:?]
2025-05-20 16:55:23.927   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 1 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:55:25.513   INFO    26 --- [     pool-2-thread-1] c.u.u.i.WorkIdClient                     : renewWorkId primaryId=489,workId=426,result=
2025-05-20 16:55:26.937   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 2 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:55:30.950   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 4 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:55:35.433   INFO    26 --- [     pool-2-thread-1] c.u.u.i.WorkIdClient                     : renewWorkId primaryId=489,workId=426,result=
2025-05-20 16:55:36.962   WARN    44 --- [figLongPollService-1] c.c.f.a.i.RemoteConfigLongPollService    : Long polling failed, will retry in 8 seconds. appId: upex-reconciliation, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=upex-reconciliation&ip=************ [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-05-20 16:55:37.523   INFO    27 --- [            Thread-1] c.u.u.i.WorkIdClient                     : releaseWorkId primaryId=489,workId=426,result=1
2025-05-20 16:55:49.315   INFO     1 --- [                main] c.u.r.j.ReconciliationJobApplication     : upex-reconciliation-job is starting
2025-05-20 16:55:49.318   INFO     1 --- [                main] c.u.u.u.EnvUtils                         : EnvUtils env systemEnv = null envType=null
