package com.upex.reconciliation.facade.dto.results;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自有资产系统账户明细响应
 *
 * <AUTHOR>
 * @date 2023/6/26 10:48
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
public class OwnAssetAccountDetailRsp {
    /**
     * 总资产类型号
     */
    private Integer totalAssetTypeCode;
    /**
     * 总资产类型名称
     */
    private String totalAssetTypeName;
    /**
     * 资产子类型号
     */
    private Integer childAssetTypeCode;
    /**
     * 资产子类型名称
     */
    private String childAssetTypeName;
    /**
     * 账户类型号
     */
    private Integer accountTypeCode;
    /**
     * 账户类型名称
     */
    private String accountTypeName;
    /**
     * 账户标识
     */
    private String accountMarker;
    /**
     * 币种名称
     */
    private String coinName;
    /**
     * 汇率
     */
    private String rate;
    /**
     * 币种数量
     */
    private String coinCount;
}
