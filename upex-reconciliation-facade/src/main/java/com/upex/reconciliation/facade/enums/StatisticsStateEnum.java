package com.upex.reconciliation.facade.enums;

import com.upex.commons.support.exception.ApiException;

/**
 * @<PERSON>
 * @Date 2021/10/21 下午2:43
 * @Description 统计任务状态
 */
public enum StatisticsStateEnum {

    /**
     * 初始状态
     */
    INIT(0, "初始状态"),
    /**
     * 统计未完成：对帐异常导致统计异常，即此状态反应此次统计只包含虚增，钱包，挪用；
     */
    NOT_DONE(1, "统计未完成"),
    /**
     * 外部资产统计完成
     */
    FINANCIAL_DONE(3, "财务资产统计完成"),
    /**
     * 外部资产统计完成
     */
    EXTERNAL_DONE(4, "外部资产统计完成"),
    /**
     * 统计完成
     */
    DONE(2, "统计完成");


    private Integer status;
    private String desc;

    StatisticsStateEnum(Integer code, String desc) {
        status = code;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static StatisticsStateEnum toEnum(Integer code) {
        for (StatisticsStateEnum item : StatisticsStateEnum.values()) {
            if (item.status.equals(code)) {
                return item;
            }
        }
        throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
    }

}
