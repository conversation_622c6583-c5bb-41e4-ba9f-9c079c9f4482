package com.upex.reconciliation.facade.enums;

import com.upex.utils.framework.IErrorCode;


/**
 * <AUTHOR>
 */
public enum BillExceptionEnum implements IErrorCode {

    /**
     * 系统异常
     */
    SYSTEM_ERROR("80000"),
    /**
     * 非法参数
     */
    ILLEGAL_PARAMS("80001"),
    /**
     * 币种不存在
     */
    COIN_NOT_EXISTS("80002"),
    /**
     * 参数校验失败
     */
    PARAMS_VALIDATE_ERROR("80003"),
    /**
     * 用户不存在
     */
    USER_NOT_EXISTS("80004"),
    /**
     * 汇率为0
     */
    RATE_NOT_EXISTS("80005"),
    /**
     * 交易对不存在
     */
    TRADING_ON_NOT_EXISTS("80006"),
    /**
     * 当前用户非系统用户
     */
    USER_NOT_SYSTEM_USER("80007"),
    /**
     * 接口异常
     */
    ABNORMAL_INTERFACE("80008"),
    /**
     * 页号为空
     */
    PAGENO_IS_NULL("80009"),
    /**
     * 页大小为空
     */
    PAGESIZE_IS_NULL("80010"),
    /**
     * 类型为空
     */
    TYPE_IS_NULL("80011"),
    /**
     * 开始日期不能大于结束日期
     */
    TIME_ERROR("80012"),
    /**
     * 账户参数为空
     */
    ACCOUNT_PARAMS_IS_NULL("80013"),
    /**
     * 不可查看系统用户账户资金
     */
    SYSTEM_USER_ILLEGAL_PARAMS("80014"),
    /**
     * 子系统列表为空
     */
    SUBSYSTEM_LIST_IS_NULL("80015"),

    /**
     * 时间不能大于当前时间
     */
    TIME_GREATER_THAN_CURRENT_TIME("80016"),

    /**
     * 发送lark成功编码
     * 法币成功编码
     */
    SEND_LARK_SUCCESS_CODE("00000"),

    /**
     * INTEGRATION_ERROR 集成下游异常
     */
    INTEGRATION_ERROR("80017"),
    ;
    private final String code;

    BillExceptionEnum(final String code) {
        this.code = code;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return "error.code." + code;
    }

    @Override
    public String getGroup() {
        return "bill";
    }

    @Override
    public IErrorCode getSource() {
        return null;
    }

    public Integer getInteger() {
        return Integer.parseInt(this.getCode());
    }

    @Override
    public String toString() {
        return "[" + getCode() + "]" + getMsg();
    }
}
