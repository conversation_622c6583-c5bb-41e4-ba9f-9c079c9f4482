package com.upex.reconciliation.facade.dto.results;

import lombok.Data;

import java.io.Serializable;

/**
 * 代理商资产快照检查
 *
 * <AUTHOR>
 * @date 2022/12/9 15:53
 */
@Data
public class CheckBrokerUserSnapshotAssetRsp implements Serializable {
    /**
     * 执行是否成功
     */
    private boolean result;

    /**
     * 空构造函数
     */
    public CheckBrokerUserSnapshotAssetRsp() {
    }

    /**
     * 全参构造函数
     *
     * @param result
     */
    public CheckBrokerUserSnapshotAssetRsp(boolean result) {
        this.result = result;
    }
}
