package com.upex.reconciliation.facade.dto.results;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/10/18 下午5:35
 * @Description 资产详情-币种维度
 */
@Data
public class AssetsDetailResult implements Serializable {

    private static final long serialVersionUID = -7214210183368031234L;
    /**
     * 币种
     */
    private Integer coinId;
    /**
     * 币种名称
     */
    private String coinName;
    /**
     * 币种数量
     */
    private BigDecimal coinCount;
}
