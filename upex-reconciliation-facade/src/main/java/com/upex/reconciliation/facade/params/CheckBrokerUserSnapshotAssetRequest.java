package com.upex.reconciliation.facade.params;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 自有资产账户明细查询参数
 *
 * <AUTHOR>
 * @date 2023/6/26 10:36
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
public class CheckBrokerUserSnapshotAssetRequest implements Serializable {
    /**
     * 请求参数列表
     */
    private List<CheckBrokerUserSnapshotAssetParam> checkBrokerUserSnapshotAssetParamList = new ArrayList<>();

    public void verifyParam() {
        if (CollectionUtils.isEmpty(checkBrokerUserSnapshotAssetParamList)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR, "请求参数不能为空");
        }

        for (CheckBrokerUserSnapshotAssetParam param : checkBrokerUserSnapshotAssetParamList) {
            if (Objects.isNull(param)) {
                throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR, "请求对象为空");
            }
            if (Objects.isNull(param.getUserId())) {
                throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR, "参数中userId不能为null");
            }
            if (Objects.isNull(param.getSnapshotTime())) {
                throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR, "参数中snapshotTime不能为null");
            }
        }
    }
}
