package com.upex.reconciliation.facade.dto.results;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 资产分析响应
 *
 * <AUTHOR>
 * @date 2023/4/14 10:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReconAssetAnalysisUserAssetRsp implements Serializable {
    /**
     * 资产快照时间
     */
    private Long snapshotTime;

    /**
     * 用户总资产折u
     */
    private BigDecimal totalAssetUsdt;

    /**
     * 是否是数据库查询的值
     */
    private Boolean dbFlag;
}
