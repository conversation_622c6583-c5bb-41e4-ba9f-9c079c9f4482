package com.upex.reconciliation.facade.dto.results;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SysOrCustomerTotalBalanceVo implements Serializable {

    private static final long serialVersionUID = 7053788755269316523L;
    /**
     * 账户类型
     */
    private String accountType;
    /**
     * 类型：1按照币种查询，2按照账户查询
     */
    private Integer type;
    /**
     * 币种
     */
    private Integer coinId;
    /**
     * 币种名称
     */
    private String coinName;
    /**
     * 账户类型
     */
    private AccountTypeEnum accountTypeEnum;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 金额
     */
    private BigDecimal balance = BigDecimal.ZERO;
    /**
     * 页面显示金额
     */
    private String showBalance;

    @Override
    public String toString() {
        return "SysOrCustomerTotalBalanceVo{" +
                "accountType='" + accountType + '\'' +
                ", type=" + type +
                ", coinId=" + coinId +
                ", coinName='" + coinName + '\'' +
                ", accountTypeEnum=" + accountTypeEnum +
                ", userId=" + userId +
                ", balance=" + balance +
                ", showBalance='" + showBalance + '\'' +
                '}';
    }
}
