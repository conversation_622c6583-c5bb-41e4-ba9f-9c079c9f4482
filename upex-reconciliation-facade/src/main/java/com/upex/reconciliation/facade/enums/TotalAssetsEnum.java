package com.upex.reconciliation.facade.enums;

import com.upex.utils.annotation.EnumExtend;

@EnumExtend(dbs = {
        @EnumExtend.Db(dbName = "upex_bill", tableName = "bill_statistics_asset_detail", fieldName = "asset_type"),
        @EnumExtend.Db(dbName = "upex_bill", tableName = "bill_statistics_asset_type", fieldName = "asset_type")
})
public enum TotalAssetsEnum {

    /**
     * 平台总资产
     */
    @EnumExtend.Code(code = "1", desc = "平台总资产")
    PLATFORM(1, "平台总资产", "USDT", "platform"),
    /**
     * 系统资产总额
     */
    @EnumExtend.Code(code = "2", desc = "系统资产总额")
    SYSTEM(2, "系统资产总额", "USDT", "system"),
    /**
     * 客户资产总额
     */
    @EnumExtend.Code(code = "3", desc = "客户资产总额")
    CUSTOMER(3, "客户资产总额", "USDT", "customer"),
    /**
     * 平台与钱包资产差额
     * (系统资产总额) 384304.34 - 0 + (客户资产总额)-92201.29 - (钱包资产)384304.34- 0  -
     * 系统客户资产差额(系统资产总额-虚增资产+客户资产总额-钱包资产-挪用资产-swap外部钱包)
     */
    @EnumExtend.Code(code = "4", desc = "系统客户资产差额")
    DIFFERENCE(4, "系统客户资产差额", "USDT", "difference"),
    /**
     * 钱包资产
     */
    @EnumExtend.Code(code = "5", desc = "钱包资产")
    WALLET(5, "钱包资产", "USDT", "wallet"),
    /**
     * 虚增资产
     */
    @EnumExtend.Code(code = "6", desc = "虚增资产")
    VIRTUAL_ADD(6, "虚增资产", "USDT", "virtual_add"),
    /**
     * 挪用资产
     */
    @EnumExtend.Code(code = "7", desc = "挪用资产")
    APPROPRIATE(7, "挪用资产", "USDT", "appropriate"),

    /**
     * 外部平台资产
     */
    @EnumExtend.Code(code = "8", desc = "外部平台资产")
    EXTERNAL_PLATFORM(8, "外部平台资产", "USDT", "external_platform"),

    /**
     * 外部钱包资产
     */
    @EnumExtend.Code(code = "9", desc = "外部钱包资产")
    EXTERNAL_WALLET(9, "外部钱包资产", "USDT", "external_wallet"),

    /**
     * 虚增资产2
     */
    @EnumExtend.Code(code = "10", desc = "虚增资产2")
    VIRTUAL_TWO_ADD(10, "虚增资产2", "USDT", "virtual_two_add"),

    /**
     * 自动对账差额(客户资产+系统净资产（系统资产-虚增2）+固定缺口-钱包资产-挪用资产-swap钱包资产-法币资产)
     */
    @EnumExtend.Code(code = "11", desc = "自动对账差额")
    RECONCILIATION_BALANCE(11, "自动对账差额", "USDT", "reconciliation_balance"),

    /**
     * 自有资产=外部钱包资产 9 +外部平台资产 8 +钱包资产 5 +法币资产16 -客户资产 3 + swap外部钱包资产 13
     */
    @EnumExtend.Code(code = "12", desc = "自有资产")
    OWN_ASSETS(12, "自有资产", "USDT", "own_assets"),

    /**
     * swap钱包资产
     */
    @EnumExtend.Code(code = "13", desc = "swap钱包资产")
    SWAP_WALLET_ASSETS(13, "swap钱包资产", "USDT", "swap_wallet_assets"),

    /**
     * 外部矿池钱包资产
     */
    @EnumExtend.Code(code = "14", desc = "外部矿池钱包资产")
    MINING_POOL_ASSETS(14, "外部矿池钱包资产", "USDT", "mining_pool_assets"),

    /**
     * 固定缺口资产
     */
    @EnumExtend.Code(code = "15", desc = "固定缺口资产")
    FIXED_GAP_ASSETS(15, "固定缺口资产", "USDT", "fixed_gap_assets"),

    /**
     * 法币资产
     */
    @EnumExtend.Code(code = "16", desc = "法币资产")
    LEGAL_CURRENCY_ASSETS(16, "法币资产", "USDT", "legal_currency_assets"),

    /**
     * 废弃资产
     */
    @EnumExtend.Code(code = "17", desc = "废弃资产")
    ABANDONED_ASSETS(17, "废弃资产", "USDT", "abandoned_assets"),

    /**
     * 对账资产
     */
    @EnumExtend.Code(code = "18", desc = "对账资产")
    BILL_ASSETS(18, "对账资产", "USDT", "bill_assets"),

    ;

    private Integer type;
    private String desc;
    private String coin;
    private String name;

    TotalAssetsEnum(Integer type, String desc, String coin, String name) {
        this.type = type;
        this.desc = desc;
        this.coin = coin;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public String getCoin() {
        return coin;
    }

    public String getName() {
        return name;
    }

    public static TotalAssetsEnum toEnum(Integer type) {
        for (TotalAssetsEnum item : TotalAssetsEnum.values()) {
            if (item.type.equals(type)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 是否钱包
     *
     * @return
     */
    public boolean isWallet() {
        return this == WALLET;
    }
}
