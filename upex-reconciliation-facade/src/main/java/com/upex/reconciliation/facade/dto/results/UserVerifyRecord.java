package com.upex.reconciliation.facade.dto.results;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户验证记录
 *
 * <AUTHOR>
 * @date 2022/11/25 18:03
 */
@Data
public class UserVerifyRecord implements Serializable {

    /**
     * 用户加密uid
     */
    private String encryptUserId;
    /**
     * 用户节点
     */
    private String merkleLeaf;
    /**
     * 所属节点位置
     */
    private Long position;
    /**
     * 验证时间
     */
    private String verifyTime;
}
