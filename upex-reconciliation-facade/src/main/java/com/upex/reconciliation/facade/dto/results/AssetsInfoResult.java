package com.upex.reconciliation.facade.dto.results;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: allen
 * @Date: 2020-05-12 19:42
 * @DES: 账户资产信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AssetsInfoResult implements Serializable {
    private static final long serialVersionUID = 5789412675627728054L;

    /**
     * 同步id
     */
    private Long syncId;

    /**
     * 账单流水类型
     */
    private String bizType;

    /**
     * 币种ID
     */
    private Integer coinId;

    /**
     * 资产属性1
     */
    private BigDecimal prop1 = BigDecimal.ZERO;

    /**
     * 资产属性2
     */
    private BigDecimal prop2 = BigDecimal.ZERO;

    /**
     * 资产属性3
     */
    private BigDecimal prop3 = BigDecimal.ZERO;

    /**
     * 资产属性4
     */
    private BigDecimal prop4 = BigDecimal.ZERO;

    /**
     * 资产属性5
     */
    private BigDecimal prop5 = BigDecimal.ZERO;

    /**
     * 备用参数
     */
    private String params;


    /**
     * 基于coinId合并数据
     *
     * @param assetsInfoResult
     */
    public void mergeByCoinId(AssetsInfoResult assetsInfoResult) {
        if (assetsInfoResult == null) {
            return;
        }
        if (!assetsInfoResult.getCoinId().equals(assetsInfoResult.getCoinId())) {
            return;
        }
        setProp1(getProp1().add(assetsInfoResult.getProp1()));
        setProp2(getProp2().add(assetsInfoResult.getProp2()));
        setProp3(getProp3().add(assetsInfoResult.getProp3()));
        setProp4(getProp4().add(assetsInfoResult.getProp4()));
        setProp5(getProp5().add(assetsInfoResult.getProp5()));
    }

    /**
     * prop1+prop2+prop3+prop4+prop5
     *
     * @return
     */
    public BigDecimal getPropSum() {
        return prop1.add(prop2).add(prop3).add(prop4).add(prop5);
    }

    /**
     * prop2+prop3
     *
     * @return
     */
    public BigDecimal getProp2Prop3Sum() {
        return prop2.add(prop3);
    }
}
