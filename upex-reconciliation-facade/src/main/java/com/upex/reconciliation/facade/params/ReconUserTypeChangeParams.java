package com.upex.reconciliation.facade.params;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReconUserTypeChangeParams implements Serializable {

    private static final long serialVersionUID = 8179655564791789794L;
    /**
     * 开始时间
     */
    private Long beginTime;
    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * 请求账户类型
     */
    private Integer accountType;
    /**
     * 账户参数(杠杆为交易对)
     */
    private String accountParam;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 币种
     */
    private Integer coinId;
    /**
     * 页号
     */
    private Integer pageNo;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 合约类型 1代表usdt合约/2代表币本位合约
     */
    private Integer type;

}
