package com.upex.reconciliation.facade.params;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 自有资产资产子类型明细查询参数
 *
 * <AUTHOR>
 * @date 2023/6/26 10:36
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
public class OwnChildAssetDetailRequest extends BillBaseParam {

    /**
     * 快照时间
     */
    private Long snapshotTime;
    /**
     * 总资产类型
     */
    private Integer totalAssetTypeCode;
    /**
     * 总资产类型名称
     */
    private String totalAssetTypeName;
    /**
     * 资产子类型
     */
    private Integer childAssetTypeCode;
    /**
     * 资产子类型名称
     */
    private String childAssetTypeName;
    /**
     * 币种id
     */
    private Integer coinId;
    /**
     * 币种名称
     * 现货所有币种（包含法币），支持模糊查询，前端实现
     */
    private String coinName;

    public void validate() {
        super.verifyPageParam();
        if (Objects.isNull(snapshotTime) || Objects.isNull(totalAssetTypeCode)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
    }
}
