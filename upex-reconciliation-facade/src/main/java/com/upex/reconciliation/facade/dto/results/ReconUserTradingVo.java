package com.upex.reconciliation.facade.dto.results;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ReconUserTradingVo implements Serializable {

    private static final long serialVersionUID = -6385923781943575876L;
    /**
     * 币币盈利额
     */
    private BigDecimal SpotProfitTotal = BigDecimal.ZERO;
    /**
     * 页面显示币币盈利额
     */
    private String ShowSpotProfitTotal;

    /**
     * u合约盈亏总额
     */
    private BigDecimal uContractProfitLossTotal = BigDecimal.ZERO;
    /**
     * 页面显示u合约盈亏总额
     */
    private String showUContractProfitLossTotal;

    /**
     * 币合约盈亏总额
     */
    private BigDecimal bContractProfitLossTotal = BigDecimal.ZERO;
    /**
     * 页面显示币合约盈亏总额
     */
    private String showBContractProfitLossTotal;
}
