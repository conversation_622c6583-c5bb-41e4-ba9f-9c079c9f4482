package com.upex.reconciliation.facade.params;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 自有资产账户明细查询参数
 *
 * <AUTHOR>
 * @date 2023/6/26 10:36
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
public class CheckBrokerUserSnapshotAssetParam implements Serializable {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户快照时间
     */
    private Long snapshotTime;
    /**
     * 现货资产余额
     */
    private BigDecimal spotBalance = BigDecimal.ZERO;
    /**
     * 合约资产余额
     */
    private BigDecimal mixBalance = BigDecimal.ZERO;
    /**
     * 杠杆资产余额
     */
    private BigDecimal marginBalance = BigDecimal.ZERO;
    /**
     * 理财资产余额
     */
    private BigDecimal financialBalance = BigDecimal.ZERO;
    /**
     * P2P资产余额(OTC)
     */
    private BigDecimal p2pBalance = BigDecimal.ZERO;

}
