package com.upex.reconciliation.facade.params;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AssetsFixedGapAddParam implements Serializable {

    private static final long serialVersionUID = -2824111718935355246L;

    /**
     * 币种
     */
    @NotNull
    private Integer coinId;

    /**
     * 币种名称
     */
    @NotBlank
    private String coinName;

    /**
     * 固定缺口值
     */
    @NotNull
    private BigDecimal amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    @NotBlank
    private String creator;

}