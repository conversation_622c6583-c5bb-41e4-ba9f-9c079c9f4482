package com.upex.reconciliation.facade.dto.results;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 资产分析响应
 *
 * <AUTHOR>
 * @date 2023/4/14 10:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReconBillBaseUserAssetsUtcZeroRsp implements Serializable {
    /**
     * 数据库标识
     * 本期数据是否跑完
     */
    private Boolean isDbFlag = false;
    /**
     * 资产分析响应集合
     */
    private List<ReconBillBaseUserAssetsRsp> reconBillBaseUserAssetsRspList = new ArrayList<>();
}
