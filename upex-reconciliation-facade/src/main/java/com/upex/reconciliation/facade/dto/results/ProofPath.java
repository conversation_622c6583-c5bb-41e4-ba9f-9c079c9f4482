package com.upex.reconciliation.facade.dto.results;

import com.alibaba.fastjson.annotation.JSONField;
import com.upex.reconciliation.facade.serializer.BigDecimalToStringSerializer;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProofPath implements Serializable {
    /**
     * 审计id
     */
    private String auditId;

    /**
     * 资产集合
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private Map<String, BigDecimal> balances;

    /**
     * 随机值
     */
    private String nonce;

    /**
     * 默克尔树节点值
     */
    private String merkelLeaf;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 角色 0-空节点， 1-左节点， 2-右节点， 3-根节点
     */
    private Integer role;

    /**
     * 加密用户id
     */
    private String encryptUid;

    /**
     * 默克尔树父节点hash-用于ui页面的展示
     */
    private String parentMerkelLeaf;
}
