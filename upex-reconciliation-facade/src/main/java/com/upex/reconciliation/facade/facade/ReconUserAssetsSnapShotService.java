package com.upex.reconciliation.facade.facade;

import com.upex.reconciliation.facade.dto.results.*;
import com.upex.reconciliation.facade.params.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @DES: 用户资产情况
 */
@FeignClient(value = "${upex.feignClient.recon}",contextId = "reconUserAssetsSnapShotService",path = "/inner/v1/recon")
public interface ReconUserAssetsSnapShotService {

    /**
     * 根据用户当前时间查询不同业务系统用户资产快照
     * @param reconUserAssetsSnapShotParams
     * @return
     */
    @PostMapping("/selectCurrentSysAssetsSnapShotByTime")
    ReconBillMessagePage<ReconTotalBalanceVo> selectCurrentSysAssetsSnapShotByTime(@RequestBody ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams);

    /**
     * 查询用户出入金
     * @param reconUserTypeChangeParams
     * @return
     */
    @PostMapping("/selectOutInBalance")
    List<ReconUserOutInBalanceVo> selectOutInBalance(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams);

    /**
     * 用户出入金详情
     * @param reconUserTypeChangeParams
     * @return
     */
    @PostMapping("/selectDetails")
    ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> selectDetails(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams);

    /**
     * 其他收入
     * @param reconUserTypeChangeParams
     * @return
     */
    @PostMapping("/selectOtherIncome")
    List<ReconUserOutInBalanceDetailsVo> selectOtherIncome(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams);

    /**
     * 最后快照时间
     * @param reconUserTypeChangeParams
     * @return
     */
    @PostMapping("/selectFinalSnapShotTime")
    String selectFinalSnapShotTime(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams);


    /**
     * 查询用户当前账户情况各系统资产总和
     * @param reconUserAssetsSnapShotParams
     * @return
     */
    @PostMapping("/selectUserAssets")
    ReconUserAssetsVo selectUserAssets(@RequestBody ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams);

    /**
     * 查询币币系统交易盈亏情况
     * @param reconUserTypeChangeParams
     * @return
     */
    @PostMapping("/selectBbTrading")
    List<ReconBbTradingVo> selectBbTrading(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams);


    /**
     * 用户交易盈亏情况盈利额总额
     * @param reconUserTypeChangeParams
     * @return
     */
    @PostMapping("/selectUserProfitLossTotal")
    ReconUserTradingVo selectUserProfitLossTotal(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams);


    /**
     * 获取用户特定时间的资产详情
     * @param params
     * @return
     */
    @PostMapping(value = "/getUserAssetsBySnapShotTime")
    ReconTotalAssetsDetailVo getUserAssetsBySnapShotTime(@RequestBody ReconUserAssetsBySnapShotTimeParams params);


    /**
     * 获取用户特定时间的资产详情
     * @param reconUserAssetsParam 用户资产查询参数
     * @return {@link ReconUserAssetsResult }
     * <AUTHOR>
     * @date 2022/12/13 09:17
     */
    @PostMapping(value = "/getUserAssetsByCoinAndTime")
    List<ReconUserAssetsResult> getUserAssetsByCoinAndTime(@RequestBody ReconUserAssetsParam reconUserAssetsParam);

    /**
     * 批量获取用户特定时间的资产详情
     * @param param 查询参数
     * @return
     */
    @PostMapping(value = "/batchUserAssetsBySnapShotTime")
    Map<Long, ReconTotalAssetsDetailVo> batchUserAssetsBySnapShotTime(@RequestBody ReconBillBaseBatchUserAssetsParams param);



    /**
     * 查询u合约和币本位合约盈亏
     * @param reconUserTypeChangeParams
     * @return
     */
    @PostMapping("/selectContract")
    List<ReconContractTradingVo> selectContract(@RequestBody ReconUserTypeChangeParams reconUserTypeChangeParams);

    /**
     * 获取用户指定时间的资产快照列表
     *
     * @param userAssetsSnapShotParams
     * @return
     */
    @PostMapping("/getUserAssetsSnapShotList")
    List<UserAssetsDetailVo> getUserAssetsSnapShotList(@RequestBody UserAssetsSnapShotParams userAssetsSnapShotParams);
}
