package com.upex.reconciliation.facade.dto.results;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户验证记录
 *
 * <AUTHOR>
 * @date 2022/11/25 11:46
 */
@Data
public class UserVerifyRecordRsp implements Serializable {


    /**
     * 资产验证Id
     */
    private String auditId;
    /**
     * 开始id， 上一页赋值用
     */
    private Long startId;
    /**
     * 结束id， 下一页赋值用
     */
    private Long endId;
    /**
     * 验证总数量
     */
    private Integer total = 0;
    /**
     * 是否有下一页
     */
    private Boolean hasNext = false;
    /**
     * 是否有上一页
     */
    private Boolean hasPre = false;
    /**
     * 用户验证分页记录
     */
    private List<UserVerifyRecordVo> userVerifyRecords = new ArrayList<>();

}
