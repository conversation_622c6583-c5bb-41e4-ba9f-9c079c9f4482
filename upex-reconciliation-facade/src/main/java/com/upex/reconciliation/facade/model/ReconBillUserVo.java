package com.upex.reconciliation.facade.model;

import com.upex.reconciliation.facade.enums.WithdrawCheckResultEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 返回用户对账相应的数据
 */
@Data
public class ReconBillUserVo implements Serializable {

    private static final long serialVersionUID = 8551199026479443792L;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 对账对平时间
     */
    private Long checkOkTime;
    /**
     * 是否检测通过
     */
    private boolean isPass;
    /**
     * 响应时间
     */
    private Long responseTime;


    /**
     * 提币失败原因枚举
     */
    private WithdrawCheckResultEnum withdrawCheckResultEnum;

    private Integer withdrawCheckResultCode;
    private String withdrawCheckResultValue;
    private String withdrawCheckResultName;
    private Integer withdrawCheckResultRetryAble;
}
