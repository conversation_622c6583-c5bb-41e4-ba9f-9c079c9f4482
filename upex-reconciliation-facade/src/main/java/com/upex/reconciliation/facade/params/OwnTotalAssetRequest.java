package com.upex.reconciliation.facade.params;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;


/**
 * 自有资产折u请求参数
 *
 * <AUTHOR>
 * @date 2023/6/26 10:33
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
public class OwnTotalAssetRequest extends BillBaseParam {
    /**
     * 快照时间
     */
    private Long snapshotTime;

    public void validate() {
        super.verifyPageParam();
        if (Objects.isNull(snapshotTime)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR, "参数校验失败");
        }
    }
}
