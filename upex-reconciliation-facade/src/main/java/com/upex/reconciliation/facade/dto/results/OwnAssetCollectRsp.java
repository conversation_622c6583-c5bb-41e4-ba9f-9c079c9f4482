package com.upex.reconciliation.facade.dto.results;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自有资产折u响应
 *
 * <AUTHOR>
 * @date 2023/6/26 10:43
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
public class OwnAssetCollectRsp {
    /**
     * 总资产类目code
     */
    private Integer totalAssetTypeCode;
    /**
     * 总资产类目名称
     */
    private String totalAssetTypeName;
    /**
     * 币种名称
     * 折合币种
     */
    private String coinName;
    /**
     * 币种折U值
     */
    private String coinAmount;
}
