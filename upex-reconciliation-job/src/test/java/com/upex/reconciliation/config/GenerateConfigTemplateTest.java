package com.upex.reconciliation.config;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.AssetsCheckTypeEnum;
import org.junit.Test;

import java.util.*;

import static com.upex.reconciliation.facade.enums.AccountTypeEnum.*;

/**
 * 生成apollo配置模板
 */
public class GenerateConfigTemplateTest {

    /**
     * 生成业务线配置
     */
    @Test
    public void generateBizConfigTemplate() {
        AccountTypeEnum[] accountTypeEnums = new AccountTypeEnum[]{
                SPOT,
                LEVER_FULL,
                LEVER_ONE,
                OTC,
                USDT_MIX_CONTRACT_BL,
                USD_MIX_CONTRACT_BL,
                USDC_MIX_CONTRACT_BL,
                UNIFIED,
        };
        List<Map<String, Object>> bizConfigList = new ArrayList<>();
        for (AccountTypeEnum accountTypeEnum : accountTypeEnums) {
            Map<String, Object> bizConfigMap = buildBizConfigTemplate(accountTypeEnum);
            bizConfigList.add(bizConfigMap);
        }
        System.out.println(JSON.toJSONString(bizConfigList));
    }

    /**
     * 生成业务线配置
     */
    @Test
    public void generateLegderConfigTemplate() {
        Map<String, Object> ledgerConfigMap = new LinkedHashMap<>();
        ledgerConfigMap.put("isOpen", true);
        ledgerConfigMap.put("accountType", AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode());
        ledgerConfigMap.put("accountParam", AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getAssetsParam());
        ledgerConfigMap.put("ledgerInoutCheckOpen", true);
        ledgerConfigMap.put("ledgerCheckLCountSCountOpen", true);
        ledgerConfigMap.put("ledgerCheckContractProfitOpen", true);
        ledgerConfigMap.put("ledgerCheckProfitTransferOpen", true);
        System.out.println(JSON.toJSONString(ledgerConfigMap));
    }

    private static final Map<Byte, String> topicMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : new AccountTypeEnum[]{
                SPOT, LEVER_FULL, LEVER_ONE, OTC,
                S_USDT_MIX_CONTRACT_BL, S_USD_MIX_CONTRACT_BL,
                USDT_MIX_CONTRACT_BL, USD_MIX_CONTRACT_BL, USDC_MIX_CONTRACT_BL, S_USDC_MIX_CONTRACT_BL,UNIFIED}) {
            put(accountTypeEnum.getCode(), String.format("recon_consumer_bill_type_%d_prod", accountTypeEnum.getCode()));
        }
        for (AccountTypeEnum accountTypeEnum : new AccountTypeEnum[]{
                P_SPOT, P_LEVER_FULL, P_LEVER_ONE,
                P_USDT_MIX_CONTRACT_BL, P_USD_MIX_CONTRACT_BL, P_USDC_MIX_CONTRACT_BL,P_UNIFIED}) {
            put(accountTypeEnum.getCode(), String.format("recon_consumer_bill_type_%d_paptrading", accountTypeEnum.getCode()));
        }
        for (AccountTypeEnum accountTypeEnum : new AccountTypeEnum[]{
                DEMO_SPOT, DEMO_LEVER_FULL,
                DEMO_LEVER_ONE, DEMO_S_USDT_MIX_CONTRACT_BL,
                DEMO_S_USDC_MIX_CONTRACT_BL, DEMO_S_USD_MIX_CONTRACT_BL,
                DEMO_USDT_MIX_CONTRACT_BL, DEMO_USDC_MIX_CONTRACT_BL,
                DEMO_USD_MIX_CONTRACT_BL}) {
            put(accountTypeEnum.getCode(), String.format("recon_consumer_bill_type_%d_paptrading", accountTypeEnum.getCode()));
        }
    }};
    private static final Map<Byte, String> groupIdMap = new HashMap<>() {{
        topicMap.forEach((k, v) -> put(k, "bg_recon-upex-reconciliation-job-" + v + "_group"));
    }};
    private static final Map<Byte, Boolean> papTradingEnvironmentMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), accountTypeEnum.isPapTrading());
        }
    }};
    private static final Map<Byte, Boolean> inOutCheckOpenMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), !accountTypeEnum.isDemo());
        }
    }};
    private static final Map<Byte, Boolean> checkLCountSCountOpenMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), accountTypeEnum.isSContract());
        }
    }};
    private static final Map<Byte, Boolean> checkTradingOnMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), accountTypeEnum.isSContract());
        }
    }};
    private static final Map<Byte, Boolean> timeSliceUserAssetsNegativeOpenMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), accountTypeEnum.isLever() || accountTypeEnum.isUsdtContract());
        }
    }};
    private static final Map<Byte, Boolean> sumAccountTransferFeeFlagMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), (accountTypeEnum.isSpot() || accountTypeEnum.isLever() || accountTypeEnum.isContract()) && !accountTypeEnum.isDemo());
        }
    }};
    private static final Map<Byte, Boolean> sumLedgerPositionCountAndProfitFlagMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), (accountTypeEnum.isContract() || accountTypeEnum.isUta()) && !accountTypeEnum.isSContract() && !accountTypeEnum.isDemo());
        }
    }};
    private static final Map<Byte, Boolean> sumAccountTransferInterestFeeFlagMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), accountTypeEnum.getCode() == USDT_MIX_CONTRACT_BL.getCode() || accountTypeEnum.getCode() == S_USDT_MIX_CONTRACT_BL.getCode());
        }
    }};
    private static final Map<Byte, Long> exchangeUserIdMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            if (accountTypeEnum.isContract() && (accountTypeEnum.isStandard() || accountTypeEnum.isSContract())) {
                put(accountTypeEnum.getCode(), 9767939201L);
            } else if (accountTypeEnum.isContract() && accountTypeEnum.isPapTrading()) {
                put(accountTypeEnum.getCode(), 22574009243L);
            }
        }
    }};
    private static final Map<Byte, Long> systemFeeUserIdMap = new HashMap<>() {{
        put(SPOT.getCode(), 148553856L);
        put(LEVER_FULL.getCode(), 1922139819L);
        put(LEVER_ONE.getCode(), 1922139819L);
        put(S_USDT_MIX_CONTRACT_BL.getCode(), 519190811L);
        put(S_USD_MIX_CONTRACT_BL.getCode(), 519190811L);
        put(USDT_MIX_CONTRACT_BL.getCode(), 519190811L);
        put(USD_MIX_CONTRACT_BL.getCode(), 519190811L);
        put(USDC_MIX_CONTRACT_BL.getCode(), 519190811L);
        put(S_USDC_MIX_CONTRACT_BL.getCode(), 519190811L);
        put(P_SPOT.getCode(), 29302582441L);
        put(P_LEVER_FULL.getCode(), 29302582441L);
        put(P_LEVER_ONE.getCode(), 29302582441L);
        put(P_USDT_MIX_CONTRACT_BL.getCode(), 29302582441L);
        put(P_USD_MIX_CONTRACT_BL.getCode(), 29302582441L);
        put(P_USDC_MIX_CONTRACT_BL.getCode(), 29302582441L);
    }};
    private static final Map<Byte, Long> contractProfitTransferToUIdMap = new HashMap<>() {{
        put(USDT_MIX_CONTRACT_BL.getCode(), 7963904151L);
        put(USD_MIX_CONTRACT_BL.getCode(), 3790441202L);
        put(USDC_MIX_CONTRACT_BL.getCode(), 7963904151L);
        put(P_USDT_MIX_CONTRACT_BL.getCode(), 29302582441L);
        put(P_USD_MIX_CONTRACT_BL.getCode(), 29302582441L);
        put(P_USDC_MIX_CONTRACT_BL.getCode(), 29302582441L);
    }};
    private static final Map<Byte, Long> contractProfitTransferToUIdMapMap = new HashMap<>() {{
        put(S_USDT_MIX_CONTRACT_BL.getCode(), 7963904151L);
        put(USDT_MIX_CONTRACT_BL.getCode(), 7963904151L);
        put(P_USDT_MIX_CONTRACT_BL.getCode(), 29302582441L);
    }};
    private static final Map<Byte, String> profitTransferFeeCoinTypeMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            if (accountTypeEnum.isSpot()) {
                put(accountTypeEnum.getCode(), "413");
            } else if (accountTypeEnum.isLever()) {
                put(accountTypeEnum.getCode(), "42");
            } else if (accountTypeEnum.isContract()) {
                put(accountTypeEnum.getCode(), "trans_to_system_fee_account");
            }
        }
    }};
    private static final Map<Byte, String> profitTransferFeeRecycleCoinTypeMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            if (accountTypeEnum.isSpot()) {
                put(accountTypeEnum.getCode(), "414");
            } else if (accountTypeEnum.isLever()) {
                put(accountTypeEnum.getCode(), "43");
            } else if (accountTypeEnum.isContract()) {
                put(accountTypeEnum.getCode(), "system_fee_recycle");
            }
        }
    }};
    private static final Map<Byte, String> profitTransferInterestFeeCoinTypeMap = new HashMap<>() {{
        put(S_USDT_MIX_CONTRACT_BL.getCode(), "trans_to_system_interest_account");
        put(USDT_MIX_CONTRACT_BL.getCode(), "trans_to_system_interest_account");
        put(P_USDT_MIX_CONTRACT_BL.getCode(), "trans_to_system_interest_account");
    }};
    private static final Map<Byte, String> profitTransferInterestFeeRecycleCoinTypeMap = new HashMap<>() {{
        put(S_USDT_MIX_CONTRACT_BL.getCode(), "system_interest_recycle");
        put(USDT_MIX_CONTRACT_BL.getCode(), "system_interest_recycle");
        put(P_USDT_MIX_CONTRACT_BL.getCode(), "system_interest_recycle");
    }};
    private static final Map<Byte, String> contractInterestDealFeeBizTypeMap = new HashMap<>() {{
        put(S_USDT_MIX_CONTRACT_BL.getCode(), "settle_interest");
        put(USDT_MIX_CONTRACT_BL.getCode(), "settle_interest");
        put(P_USDT_MIX_CONTRACT_BL.getCode(), "settle_interest");
    }};
    private static final Map<Byte, Boolean> inoutCheckAccountTransferFeeFlagMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), accountTypeEnum == S_USD_MIX_CONTRACT_BL || accountTypeEnum == S_USDC_MIX_CONTRACT_BL || accountTypeEnum == S_USDT_MIX_CONTRACT_BL);
        }
    }};
    private static final Map<Byte, Boolean> inoutCheckAccountTransferInterestFeeFlagMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            put(accountTypeEnum.getCode(), accountTypeEnum == S_USDT_MIX_CONTRACT_BL);
        }
    }};
    private static final Map<Byte, String> serviceInstanceNameMap = new HashMap<>() {{
        for (AccountTypeEnum accountTypeEnum : new AccountTypeEnum[]{
                SPOT, LEVER_FULL, LEVER_ONE, OTC, FINANCIAL}) {
            put(accountTypeEnum.getCode(), "upex-reconciliation-job01");
        }
        for (AccountTypeEnum accountTypeEnum : new AccountTypeEnum[]{
                USDT_MIX_CONTRACT_BL, USD_MIX_CONTRACT_BL, USDC_MIX_CONTRACT_BL}) {
            put(accountTypeEnum.getCode(), "upex-reconciliation-job02");
        }
        for (AccountTypeEnum accountTypeEnum : new AccountTypeEnum[]{
                S_USDT_MIX_CONTRACT_BL, S_USD_MIX_CONTRACT_BL, S_USDC_MIX_CONTRACT_BL,
                DEMO_SPOT, DEMO_LEVER_FULL, DEMO_LEVER_ONE,
                DEMO_S_USDT_MIX_CONTRACT_BL, DEMO_S_USDC_MIX_CONTRACT_BL, DEMO_S_USD_MIX_CONTRACT_BL,
                DEMO_USDT_MIX_CONTRACT_BL, DEMO_USDC_MIX_CONTRACT_BL, DEMO_USD_MIX_CONTRACT_BL}) {
            put(accountTypeEnum.getCode(), "upex-reconciliation-job03");
        }
        for (AccountTypeEnum accountTypeEnum : new AccountTypeEnum[]{
                P_SPOT, P_LEVER_FULL, P_LEVER_ONE,
                P_USDT_MIX_CONTRACT_BL, P_USD_MIX_CONTRACT_BL, P_USDC_MIX_CONTRACT_BL}) {
            put(accountTypeEnum.getCode(), "upex-reconciliation-job-paptrading-0");
        }
    }};

    /**
     * 构建业务配置模板
     *
     * @return
     */
    private Map<String, Object> buildBizConfigTemplate(AccountTypeEnum accountTypeEnum) {
        Map<String, Object> bizConfigMap = new LinkedHashMap<>();
        bizConfigMap.put("isOpen", true);
        bizConfigMap.put("accountType", accountTypeEnum.getCode());
        bizConfigMap.put("accountParam", accountTypeEnum.getAccountParam());
        bizConfigMap.put("logLevelCode", 5);
        bizConfigMap.put("serviceInstanceName", serviceInstanceNameMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("papTradingEnvironment", papTradingEnvironmentMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("sumAccountTransferFeeFlag", sumAccountTransferFeeFlagMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("sumLedgerPositionCountAndProfitFlag", sumLedgerPositionCountAndProfitFlagMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("sumAccountTransferInterestFeeFlag", sumAccountTransferInterestFeeFlagMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("inOutCheckOpen", inOutCheckOpenMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("checkLCountSCountOpen", checkLCountSCountOpenMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("checkTradingOn", checkTradingOnMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("inoutCheckAccountTransferFeeFlag", inoutCheckAccountTransferFeeFlagMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("inoutCheckAccountTransferInterestFeeFlag", inoutCheckAccountTransferInterestFeeFlagMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("timeSliceNegativeOpen", true);
        bizConfigMap.put("timeSliceUserAssetsNegativeOpen", timeSliceUserAssetsNegativeOpenMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("exchangeUserId", exchangeUserIdMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("systemFeeUserId", systemFeeUserIdMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("contractProfitTransferToUId", contractProfitTransferToUIdMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("contractInterestFeeTransferToUId", contractProfitTransferToUIdMapMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("profitTransferFeeCoinType", profitTransferFeeCoinTypeMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("profitTransferFeeRecycleCoinType", profitTransferFeeRecycleCoinTypeMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("profitTransferInterestFeeCoinType", profitTransferInterestFeeCoinTypeMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("profitTransferInterestFeeRecycleCoinType", profitTransferInterestFeeRecycleCoinTypeMap.get(accountTypeEnum.getCode()));
        bizConfigMap.put("contractInterestDealFeeBizType", contractInterestDealFeeBizTypeMap.get(accountTypeEnum.getCode()));
        // 构建kafka配置
        Map<String, Object> kafkaConfigMap = new LinkedHashMap<>();
        kafkaConfigMap.put("topic", topicMap.get(accountTypeEnum.getCode()));
        kafkaConfigMap.put("groupId", groupIdMap.get(accountTypeEnum.getCode()));
        kafkaConfigMap.put("kafkaPartitionNum", 6);
        kafkaConfigMap.put("mqProducerSpeed", 300);
        kafkaConfigMap.put("mqConsumerRateLimit", 300);
        bizConfigMap.put("reconKafkaOpsConfig", kafkaConfigMap);
        return bizConfigMap;
    }
}