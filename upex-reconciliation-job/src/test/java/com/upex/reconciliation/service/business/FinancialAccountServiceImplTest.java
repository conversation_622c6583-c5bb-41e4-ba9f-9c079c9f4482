package com.upex.reconciliation.service.business;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.job.ReconciliationJobApplication;
import com.upex.reconciliation.service.dao.entity.FinancialVirtualAccount;
import com.upex.reconciliation.service.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReconciliationJobApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class FinancialAccountServiceImplTest {

    @Resource
    private FinancialVirtualAccountBizService financialVirtualAccountBizService;

    @Test
    void modifyHandlerTest() {
        List<FlatMessage> flatMessages = JSONObject.parseArray("[{\"data\":[{\"id\":\"1313655255137189888\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"15.*********\",\"create_time\":\"2025-06-03 12:42:20.016\",\"update_time\":\"2025-06-16 12:18:21.424\",\"version\":\"1318360263413923840\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"30.*********\",\"update_time\":\"2025-06-03 13:28:30.132\",\"version\":\"1313666873761923072\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1313784376769052672\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2025-06-03 21:15:25.009\",\"update_time\":\"2025-06-16 12:18:21.424\",\"version\":\"1318360263413923845\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"0.*********\",\"update_time\":\"2025-06-03 21:15:25.034\",\"version\":\"1313784376777441280\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1314636638559076352\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2025-06-06 05:42:00.060\",\"update_time\":\"2025-06-16 12:18:21.424\",\"version\":\"1318360263413923852\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"0.*********\",\"update_time\":\"2025-06-06 05:42:00.078\",\"version\":\"1314636638563270656\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1314605684771717120\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2025-06-06 03:39:00.102\",\"update_time\":\"2025-06-16 12:18:21.424\",\"version\":\"1318360263413923850\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"0.*********\",\"update_time\":\"2025-06-06 03:39:00.122\",\"version\":\"1314605684784300032\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1313655255137189888\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2025-06-03 12:42:20.016\",\"update_time\":\"2025-06-16 12:18:21.465\",\"version\":\"1318360263585890304\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"15.*********\",\"update_time\":\"2025-06-16 12:18:21.424\",\"version\":\"1318360263413923840\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"}]", FlatMessage.class);
        List<FinancialVirtualAccount> financialVirtualAccounts = buildBillChangeDataList(flatMessages);
        financialVirtualAccountBizService.modifyHandler(financialVirtualAccounts);
    }

    @Test
    void modifyHandlerTest2() {
        List<FlatMessage> flatMessages = JSONObject.parseArray("[{\"data\":[{\"id\":\"1227873832103464961\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2024-10-09 19:37:35.018\",\"update_time\":\"2025-06-16 12:18:26.273\",\"version\":\"1318360283752103944\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"0.*********\",\"update_time\":\"2025-06-15 17:15:35.038\",\"version\":\"1318072675146858496\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1065974663420989440\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"15.*********\",\"create_time\":\"2023-07-21 01:28:05.256\",\"update_time\":\"2025-06-16 12:18:26.273\",\"version\":\"1318360283752103939\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"30.*********\",\"update_time\":\"2025-06-16 12:18:25.949\",\"version\":\"1318360282393149440\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1221552932034859008\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2024-09-22 09:00:35.011\",\"update_time\":\"2025-06-16 12:18:26.273\",\"version\":\"1318360283752103950\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"19.*********\",\"update_time\":\"2025-06-12 00:18:20.104\",\"version\":\"1316729512482242560\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1318072507513110528\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2025-06-15 17:14:55.052\",\"update_time\":\"2025-06-16 12:18:26.273\",\"version\":\"1318360283752103938\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"0.*********\",\"update_time\":\"2025-06-15 17:14:55.072\",\"version\":\"1318072507517304832\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1277362362076520448\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2025-02-23 09:07:20.018\",\"update_time\":\"2025-06-16 12:18:26.288\",\"version\":\"1318360283815018496\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"0.*********\",\"update_time\":\"2025-06-15 16:05:45.086\",\"version\":\"1318055101214425088\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1254474464625053696\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2024-12-22 05:19:00.025\",\"update_time\":\"2025-06-16 12:18:26.289\",\"version\":\"1318360283819212800\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"0.*********\",\"update_time\":\"2025-06-11 02:03:50.053\",\"version\":\"1316393674347053056\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"},{\"data\":[{\"id\":\"1065974663420989440\",\"account_id\":\"**********\",\"coin_id\":\"3\",\"coin_name\":\"ETH\",\"group_type\":\"6\",\"total_balance\":\"0.*********\",\"create_time\":\"2023-07-21 01:28:05.256\",\"update_time\":\"2025-06-16 12:18:26.327\",\"version\":\"1318360283978596352\",\"pre_settle_interest\":\"0.*********\"}],\"database\":\"upex_financial\",\"es\":\"*************\",\"id\":0,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"account_id\":\"bigint\",\"coin_id\":\"int\",\"coin_name\":\"varchar(32)\",\"group_type\":\"tinyint\",\"total_balance\":\"decimal(36,16)\",\"create_time\":\"datetime(3)\",\"update_time\":\"datetime(3)\",\"version\":\"bigint\",\"pre_settle_interest\":\"decimal(36,16)\"},\"old\":[{\"total_balance\":\"15.*********\",\"update_time\":\"2025-06-16 12:18:26.273\",\"version\":\"1318360283752103939\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":-5,\"coin_id\":4,\"coin_name\":12,\"group_type\":-6,\"total_balance\":3,\"create_time\":93,\"update_time\":93,\"version\":-5,\"pre_settle_interest\":3},\"table\":\"financial_virtual_account\",\"ts\":\"*************\",\"type\":\"UPDATE\"}]", FlatMessage.class);
        List<FinancialVirtualAccount> financialVirtualAccounts = buildBillChangeDataList(flatMessages);
        financialVirtualAccountBizService.modifyHandler(financialVirtualAccounts);
    }

    private List<FinancialVirtualAccount> buildBillChangeDataList(List<FlatMessage> flatMessages) {
        List<FinancialVirtualAccount> list = new ArrayList<>();
        for (FlatMessage flatMessage : flatMessages) {
            String typeStr = flatMessage.getType();
            boolean isDdl = flatMessage.getIsDdl();
            if (isDdl) {
                return Collections.emptyList();
            }
            SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
            if (null == sqlTypeEnum) {
                log.error("unable to resolve sqlType:{}", typeStr);
                return Collections.emptyList();
            }
            List<Map<String, String>> dataList = flatMessage.getData();
            switch (sqlTypeEnum) {
                case UPDATE:
                case INSERT:
                    list.addAll(messageDecode(dataList, flatMessage));
            }
        }
        return list;
    }

    private List<FinancialVirtualAccount> messageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage) {
        Map<String, String> oldMap = CollectionUtils.isEmpty(flatMessage.getOld()) ? new HashMap<>() : flatMessage.getOld().get(0);

        List<FinancialVirtualAccount> coList = new ArrayList<>();
        for (Map<String, String> map : dataList) {
            FinancialVirtualAccount co = new FinancialVirtualAccount();
            co.setId(map.get("id") == null ? null : Long.valueOf(map.get("id")));
            co.setUserId(map.get("account_id") == null ? null : Long.parseLong(map.get("account_id")));
            co.setCoinId(map.get("coin_id") == null ? null : Integer.valueOf(map.get("coin_id")));
            co.setCoinName(map.get("coin_name") == null ? null : map.get("coin_name"));
            co.setGroupType(map.get("group_type") == null ? null : Integer.valueOf(map.get("group_type")));
            co.setTotalBalance(map.get("total_balance") == null ? null : new BigDecimal(map.get("total_balance")));
            co.setSourceCreateTime(map.get("create_time") == null ? null : DateUtil.getMillisecondDate(map.get("create_time")));
            co.setSourceUpdateTime(map.get("update_time") == null ? null : DateUtil.getMillisecondDate(map.get("update_time")));
            co.setVersion(map.get("version") == null ? null : Long.parseLong(map.get("version")));
            co.setOldTotalBalance(oldMap.get("total_balance") == null ? null : new BigDecimal(oldMap.get("total_balance")));
            co.setOldUpdateTime(oldMap.get("update_time") == null ? null : DateUtil.getMillisecondDate(oldMap.get("update_time")));
            co.setPreSettleInterest(map.get("pre_settle_interest") == null ? null : new BigDecimal(map.get("pre_settle_interest")));
            Date createTime = new Date();
            co.setCreateTime(createTime);
            co.setUpdateTime(createTime);
            coList.add(co);
        }
        return coList;
    }

}
