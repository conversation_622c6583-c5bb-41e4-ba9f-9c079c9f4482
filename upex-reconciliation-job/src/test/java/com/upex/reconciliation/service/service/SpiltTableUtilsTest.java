package com.upex.reconciliation.service.service;

import com.upex.reconciliation.job.ReconciliationJobApplication;
import com.upex.reconciliation.service.business.createtablebyroute.BillCoinTypeUserPropertyTableCreator;
import com.upex.reconciliation.service.business.createtablebyroute.BillContractProfitTransferTableCreator;
import com.upex.reconciliation.service.business.createtablebyroute.BillUserPositionTableCreator;
import com.upex.reconciliation.service.business.createtablebyroute.ReconCreateTableFactory;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@RunWith(SpringRunner.class)
@ActiveProfiles("local")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@SpringBootTest(classes = ReconciliationJobApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SpiltTableUtilsTest {

    @Resource
    ReconCreateTableFactory reconCreateTableFactory;

    @Resource
    BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;

    @Resource
    BillUserPositionService billUserPositionService;

    @Resource
    BillUserPositionTableCreator billUserPositionTableCreator;

    @Resource
    BillCoinTypeUserPropertyTableCreator billCoinTypeUserPropertyTableCreator;

    @Resource
    BillContractProfitTransferTableCreator billContractProfitTransferTableCreator;

    @Test
    public void testGetDateTableNameSuffix() {
//        Integer day=7;
//        if (day != null && day > 0) {
//            Date nowDate = new Date();
//            for (int i = 0; i < day; i++) {
//                String tableSuffix = SplitTableUtils.getTableNameSuffixForDay(DateUtil.addDay(nowDate, i));
//                System.out.println(tableSuffix);
//            }
//        }
    }

    @Test
    public void createTable() {
        /**
         * [
         *     {
         *         "tableType": "billCoinTypeUserProperty",
         *         "tableRouteRuleMap": {
         *               "default": {
         *                 "rule": "day",
         *                 "effectiveTime": "*************",
         *                 "accountTypes": [
         *                    10,12,21,22,30,50,52,53,54,80,100
         *                 ]
         *             }
         *         }
         *     },
         *     {
         *         "tableType": "billContractProfitTransfer",
         *         "tableRouteRuleMap": {
         *             "default": {
         *                 "rule": "month",
         *                 "effectiveTime": "*************"
         *             }
         *         }
         *     },
         *     {
         *         "tableType": "billUserPosition",
         *         "tableRouteRuleMap": {
         *             "default": {
         *                 "rule": "day",
         *                 "effectiveTime": "*************",
         *                 "accountTypes": [
         *                     50,51,52,53,54,55,100,121,122,123,124,125,126
         *                 ]
         *             }
         *         }
         *     }
         * ]
         */
        reconCreateTableFactory.createTable();
        //不分表55 bill_coin_type_user
        //分日表 bill_coin_type_user
        // 不分表 bill_user_position
        System.out.println(billCoinTypeUserPropertyTableCreator.getTableSuffixName( "51", new Date()));
        //分日表 bill_user_position
        System.out.println(billCoinTypeUserPropertyTableCreator.getTableSuffixName("50", new Date()));
        System.out.println(billContractProfitTransferTableCreator.getTableSuffixName(new Date()));
       // billUserPositionService.selectByCheckTime(51,"default", new Date(),123456L);

    }
}
