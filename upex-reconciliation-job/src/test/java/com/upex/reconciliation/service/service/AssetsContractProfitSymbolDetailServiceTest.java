package com.upex.reconciliation.service.service;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.job.ReconciliationJobApplication;
import com.upex.reconciliation.service.common.constants.enums.ProfitTypeEnum;
import com.upex.reconciliation.service.dao.entity.AssetsContractProfitSymbolDetail;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReconciliationJobApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AssetsContractProfitSymbolDetailServiceTest {
    @Resource
    private AssetsContractProfitSymbolDetailService assetsContractProfitSymbolDetailService;
    @Resource
    private SerialNoGenerator noGenerator;

    @Test
    public void insertDeleteSelectTest() {
        Date checkTime = DateUtil.getDefaultDate("2025-02-06 16:00:00");
        Date nowDate = new Date();
        List<AssetsContractProfitSymbolDetail> assetsContractProfitSymbolDetailList = new ArrayList<>();
        long batchNo = noGenerator.nextNo();
        AssetsContractProfitSymbolDetail assetsContractProfitSymbolDetail = new AssetsContractProfitSymbolDetail();
        assetsContractProfitSymbolDetail.setId(noGenerator.nextNo());
        assetsContractProfitSymbolDetail.setBatchNo(batchNo);
        assetsContractProfitSymbolDetail.setAccountType(AccountTypeEnum.INTERNAL.getCode());
        assetsContractProfitSymbolDetail.setAccountParam(AccountTypeEnum.INTERNAL.getAccountParam());
        assetsContractProfitSymbolDetail.setCheckOkTime(checkTime);
        assetsContractProfitSymbolDetail.setProfitType(ProfitTypeEnum.SYMBOL_PROFIT.getCode());
        assetsContractProfitSymbolDetail.setSymbolId("BTC_USDT1");
        assetsContractProfitSymbolDetail.setCoinId(1);
        assetsContractProfitSymbolDetail.setChangeRealizedCount(BigDecimal.TEN);
        assetsContractProfitSymbolDetail.setRealizedCount(BigDecimal.TEN);
        assetsContractProfitSymbolDetail.setUnrealizedCount(BigDecimal.TEN);
        assetsContractProfitSymbolDetail.setInitCount(BigDecimal.TEN);
        assetsContractProfitSymbolDetail.setProfitCount(BigDecimal.TEN);
        assetsContractProfitSymbolDetail.setCreateTime(nowDate);
        assetsContractProfitSymbolDetail.setUpdateTime(nowDate);
        assetsContractProfitSymbolDetailList.add(assetsContractProfitSymbolDetail);
        assetsContractProfitSymbolDetailService.batchInsert(AccountTypeEnum.INTERNAL.getBizTag(), AccountTypeEnum.INTERNAL.getAccountParam(), assetsContractProfitSymbolDetailList);

        List<AssetsContractProfitSymbolDetail> lastAssetsContractProfitSymbolDetailList = assetsContractProfitSymbolDetailService.selectListByAccountTypeAndCheckTime(AccountTypeEnum.INTERNAL.getBizTag(), AccountTypeEnum.INTERNAL.getAccountParam(), checkTime);
        log.info("lastAssetsContractProfitSymbolDetailList1:{}", JSON.toJSONString(lastAssetsContractProfitSymbolDetailList));

        assetsContractProfitSymbolDetailService.deleteByCheckTime(AccountTypeEnum.INTERNAL.getBizTag(), AccountTypeEnum.INTERNAL.getAccountParam(), DateUtil.addMinute(checkTime, -10), 1000L);

        lastAssetsContractProfitSymbolDetailList = assetsContractProfitSymbolDetailService.selectListByAccountTypeAndCheckTime(AccountTypeEnum.INTERNAL.getBizTag(), AccountTypeEnum.INTERNAL.getAccountParam(), checkTime);
        log.info("lastAssetsContractProfitSymbolDetailList2:{}", JSON.toJSONString(lastAssetsContractProfitSymbolDetailList));
    }
}
