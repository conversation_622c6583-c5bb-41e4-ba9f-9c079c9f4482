package com.upex.reconciliation.kafka;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.upex.mixcontract.common.literal.enums.FinanceBizTypeEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.TimeUnitEnum;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.DateUtil;
import groovy.lang.Tuple3;
import io.lettuce.core.output.ScanOutput;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.utils.DateUtil.FMT_yyyy_MM_dd_HH_mm_ss;

@Slf4j
public class SpringElTest {
    @Test
    public void springEl() throws Exception {
        String jsonString = JSON.toJSONString(Pair.of(111L, 222L));
        System.out.println(jsonString);
        Pair<Long, Long> pair = JSONObject.parseObject(jsonString, Pair.class);
        System.out.println(JSON.toJSONString(pair));

        String symbolId = "btc_usdt";
        Date checkTime = new Date(1747123680000L);
        // 0=symbolId,1=startTime,2=endTime
        String[] split = "btc_usdt:1747123680000:1747123740000".split(":");
        if (symbolId.equals(split[0]) && checkTime.getTime() >= Long.parseLong(split[1]) && checkTime.getTime() <= Long.parseLong(split[2])) {
            System.out.println("match");
        }else{
            System.out.println("not match");
        }
        if (true) {
            return;
        }
        Set<String> stringSet = new HashSet<>();
        System.out.println(stringSet.contains(""));

        Map<Integer, Long> latestPartitionBizTime = new HashMap<>();
        latestPartitionBizTime.put(1, 1630460400000L);
        latestPartitionBizTime.put(2, 1630460400001L);
        latestPartitionBizTime.put(3, 1630460400002L);
        System.out.println(latestPartitionBizTime.values().stream().max(Comparator.comparing(x -> x)).orElse(null));
        if (true) {
            return;
        }

        CommonBillChangeData commonBillChangeData11111 = CommonBillChangeData.builder()
                .build();
        if (true) {
            return;
        }
        Date datea = DateUtil.str2date("2021-09-01 10:00:00", FMT_yyyy_MM_dd_HH_mm_ss);
        System.out.println(DateUtil.date2str(TimeUnitEnum.toDate(datea, "MONTH:-2")));
        if (true) {
            return;
        }
        Map<Integer, Tuple3> map1 = new HashMap<>();
        map1.put(1, new Tuple3("lever_one-26", -0.8686516802999907, "lever_one-26"));
        map1.put(2, new Tuple3("spot-236", -11.4034929445362382, "spot-236"));
        System.out.println(JSON.toJSONString(map1));
        String json = "{\n" +
                "    \"lever_one-26\": -0.8686516802999907,\n" +
                "    \"spot-236\": -11.4034929445362382,\n" +
                "    \"spot-358\": -0.386063463324,\n" +
                "    \"spot-237\": 11.4034929445362382,\n" +
                "    \"spot-234\": -1238.277,\n" +
                "    \"spot-235\": 1238.277,\n" +
                "    \"spot-359\": 0.386063463324,\n" +
                "    \"mix-trans_from_otc\": 55.37,\n" +
                "    \"otc-1\": 6393.20763487,\n" +
                "    \"otc-2\": -8961.9840677,\n" +
                "    \"lever_full-42\": 52.871002,\n" +
                "    \"lever_one-32\": -25,\n" +
                "    \"lever_full-6\": -1.00000584,\n" +
                "    \"lever_full-18\": 0.00041388,\n" +
                "    \"lever_full-5\": 1,\n" +
                "    \"lever_one-15\": -82.97958489,\n" +
                "    \"lever_full-2\": -745.29752526,\n" +
                "    \"lever_full-1\": 4,\n" +
                "    \"spot-405\": 9.950942,\n" +
                "    \"spot-406\": 9.950942,\n" +
                "    \"spot-6\": -267973.73290195,\n" +
                "    \"mix-trans_from_cross\": 5.56072526,\n" +
                "    \"spot-5\": 237534.49202711,\n" +
                "    \"spot-69\": -5439.56653335,\n" +
                "    \"lever_full-13\": 430.621743,\n" +
                "    \"lever_full-14\": -766.4798019498,\n" +
                "    \"lever_one-25\": 2.1179447926603074,\n" +
                "    \"lever_full-15\": -0.00466314,\n" +
                "    \"mix-trans_to_exchange\": -224712.36183402,\n" +
                "    \"mix-trans_from_exchange\": 260349.6014918,\n" +
                "    \"lever_full-17\": -4535.93375527,\n" +
                "    \"spot-173\": 33.52202691,\n" +
                "    \"spot-174\": -33.52202691,\n" +
                "    \"spot-171\": 15395.499176015708,\n" +
                "    \"spot-172\": -15395.499176015708,\n" +
                "    \"spot-214\": 66.0062106,\n" +
                "    \"spot-137\": -1335.3264,\n" +
                "    \"spot-413\": 246.1586961708854601,\n" +
                "    \"spot-215\": 2148.5055940105424643,\n" +
                "    \"spot-138\": 1335.3264,\n" +
                "    \"spot-213\": -66.0062106,\n" +
                "    \"spot-216\": -2148.5055940105424643,\n" +
                "    \"spot-78\": 4.7026588707876193,\n" +
                "    \"lever_one-5\": 4534.93375527,\n" +
                "    \"spot-418\": -19.901884,\n" +
                "    \"lever_one-6\": -2.19979254,\n" +
                "    \"spot-70\": 5439.56653335,\n" +
                "    \"spot-71\": 10876.16021174,\n" +
                "    \"spot-72\": -10876.16021174,\n" +
                "    \"lever_full-23\": 3.1993845,\n" +
                "    \"lever_one-13\": 413044.47398816,\n" +
                "    \"lever_one-1\": 202.5252392,\n" +
                "    \"lever_one-14\": -438107.68010384761138,\n" +
                "    \"lever_full-27\": 0.8686516802999907,\n" +
                "    \"mix-trans_to_otc\": -2144.02883652,\n" +
                "    \"lever_one-2\": -11.78195279,\n" +
                "    \"lever_full-28\": -2.1179447926603074,\n" +
                "    \"spot-144\": 16.218542810804979,\n" +
                "    \"spot-143\": -16.218542810804979,\n" +
                "    \"spot-102\": -612101.8150964743296186,\n" +
                "    \"spot-103\": 637404.2929465055707113,\n" +
                "    \"lever_full-33\": 25,\n" +
                "    \"spot-80\": -4.7026588707876193\n" +
                "}";
        JSONObject jsonObject = JSON.parseObject(json);
        BigDecimal total = new BigDecimal("0.00000000");
        for (String key : jsonObject.keySet()) {
            BigDecimal bigDecimal = jsonObject.getBigDecimal(key);
            total = total.add(bigDecimal);
        }
        System.out.println(total);
        System.out.println(Duration.ofMinutes(1).toMillis());
        if (true) {
            return;
        }

        BigDecimal unProfitTransfers = new BigDecimal("0.00000167");
        System.out.println(unProfitTransfers.toPlainString());


        SortedMap<Long, Map<String, Boolean>> ledgerTimeSliceKafkaComplete = Collections.synchronizedSortedMap(new TreeMap<>());
        ledgerTimeSliceKafkaComplete.computeIfAbsent(1L, key -> new ConcurrentHashMap<>()).put("10", Boolean.TRUE);
        ledgerTimeSliceKafkaComplete.computeIfAbsent(1L, key -> new ConcurrentHashMap<>()).put("20", Boolean.TRUE);
        ledgerTimeSliceKafkaComplete.computeIfAbsent(1L, key -> new ConcurrentHashMap<>()).put("20", Boolean.FALSE);
        ledgerTimeSliceKafkaComplete.computeIfAbsent(1L, key -> new ConcurrentHashMap<>()).put("30", Boolean.FALSE);

        System.out.println(new BigDecimal(-10).negate());
        String sqlLeng = "[{\"offset\":\"312346925\",\"partition\":0},{\"offset\":\"296511769\",\"partition\":1},{\"offset\":\"294172877\",\"partition\":2},{\"offset\":\"286104408\",\"partition\":3},{\"offset\":\"299574316\",\"partition\":4},{\"offset\":\"291369712\",\"partition\":5}]\t";
        System.out.println(sqlLeng.length());
        if (true) {
            return;
        }
        CommonBillChangeData commonBillChangeData1 = new CommonBillChangeData();
        CommonBillChangeData spotInfoCommonBillChangeData = commonBillChangeData1.clone();
        spotInfoCommonBillChangeData.setCoinId(1);
        commonBillChangeData1.setCoinId(2);
        System.out.println(spotInfoCommonBillChangeData);

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), new ThreadPoolExecutor.AbortPolicy());
        List<Future<Integer>> futures = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            Future<Integer> submit = threadPoolExecutor.submit(() -> {
                try {
                    Thread.sleep(10 * 1000);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                System.out.println("线程执行完成");
                return 1;
            });
            futures.add(submit);
        }
        for (int i = 0; i < futures.size(); i++) {
            try {
                Integer integer = futures.get(i).get(3, TimeUnit.SECONDS);
                System.out.println("线程结果:" + integer);
            } catch (Exception e) {
                System.out.println("线程超时");
                e.printStackTrace();
            }
        }
        Thread.sleep(Integer.MAX_VALUE);
        if (true) {
            return;
        }
        Date timeDate = DateUtil.str2date("2024-05-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        String time1 = DateUtil.date2str(timeDate, "HH:mm:ss");

        Map<String, BigDecimal> concurrentHashMap = new ConcurrentHashMap<>();
        concurrentHashMap.computeIfPresent("11", (key, oldValue) -> oldValue.add(BigDecimal.ONE));
        concurrentHashMap.computeIfPresent("11", (key, oldValue) -> oldValue.add(BigDecimal.ONE));

        Date fristCreateTime = new Date(0L);
        System.out.println(DateUtil.date2str(fristCreateTime));
        if (true) {
            return;
        }
        StopWatch stopWatch1 = new StopWatch();
        stopWatch1.start("start");
        Thread.sleep(1000);
        stopWatch1.stop();
        stopWatch1.start("start1");
        Thread.sleep(2000);
        stopWatch1.stop();
        System.out.println(stopWatch1.prettyPrint());

        long time = 1715060386000L;
        System.out.println(time / BillConstants.ONE_MINE_MIL_SEC * BillConstants.ONE_MINE_MIL_SEC);

        byte sdfd = 127;
        System.out.println(sdfd);

        Integer ss = 1;
        List<Integer> list = new ArrayList<>();
        list.add(new Integer(1));
        System.out.println(list.contains(ss));
        if (true) {
            return;
        }

        Byte accountType = 1;
        System.out.println(Optional.ofNullable(accountType).orElse((byte) 0).toString());
        accountType = null;
        System.out.println(Optional.ofNullable(accountType).orElse((byte) 0).toString());
        if (true) {
            return;
        }
        Object arg = new Object[]{"sadfdas", "asdfsadf"};
        System.out.println(arg instanceof Object[]);
        arg = new HashMap<>();
        System.out.println(arg instanceof Map);
        if (true) {
            return;
        }
        List<String> tables = new ArrayList<>();
        tables.add("bill_user_position_50_default_20240325");
        tables.add("bill_user_position_50_default_20240326");
        tables.add("bill_user_position_50_default_20240327");
        tables.add("bill_user_position_50_default_20240328");
        tables.add("bill_user_position_50_default_20240331");
        tables.add("bill_user_position_50_default_20240405");
        String deleteTableName = "bill_user_position_50_default_20240331";
        List<String> deleteTables = tables.stream().filter(tableName -> tableName.compareTo(deleteTableName) <= 0).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(deleteTables));
        if (true) {
            return;
        }
        Date nowDate = DateUtil.str2date(DateUtil.date2str(new Date()), "yyyy-MM-dd");
        Date date = DateUtil.addMonth(nowDate, -1);
        System.out.println(DateUtil.date2str(date));
        if (true) {
            return;
        }
        Set<Integer> set1 = new HashSet<>();
        set1.add(1);
        set1.add(2);
        set1.add(3);
        set1.add(4);
        set1.add(5);
        Set<Integer> set2 = new HashSet<>();
        set2.add(3);
        set2.add(4);
        set2.add(5);
        set2.add(6);
        set2.add(7);
        set1.retainAll(set2);
        System.out.println(set1);
        if (true) {
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("init data start");
        Cache<String, Integer> cache = Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofDays(1))
                .removalListener((key, value, cause) -> {
                    System.out.println(String.format("key %s was removed %s - %s", key, value, cause));
                })
                .build();
        Integer sss = cache.get("sss", key -> 1);
//        for (int i = 0; i < 100; i++) {
//            cache.put("name" + i, i);
//        }
        for (int i = 0; i < 1_000_000; i++) {
            cache.put("name" + i, i);
        }
        stopWatch.stop();
        Random random = new Random();
        for (int i = 0; i < 100; i++) {
            String key = "name" + random.nextInt(1_000_000);
            stopWatch.start("get key " + key);
            System.out.println(cache.getIfPresent("name" + random.nextInt(1_000_000)));
            Integer value = random.nextInt(1_000_000);
            String key1 = "name" + +value;
            cache.put(key1, value);
            stopWatch.stop();
        }
        System.out.println(stopWatch.prettyPrint());
        Thread.sleep(Integer.MAX_VALUE);
        if (true) {
            return;
        }
        Date date1 = DateUtil.str2date("2024-03-21 10:00:00", FMT_yyyy_MM_dd_HH_mm_ss);
        Date date2 = DateUtil.str2date("2024-03-21 10:05:00", FMT_yyyy_MM_dd_HH_mm_ss);
        Date date3 = DateUtil.str2date("2024-03-21 10:10:00", FMT_yyyy_MM_dd_HH_mm_ss);
        List<BillCoinUserProperty> billCoinUserPropertyList = new ArrayList<>();
        BillCoinUserProperty billCoinUserProperty3 = new BillCoinUserProperty();
        billCoinUserProperty3.setId(3L);
        billCoinUserProperty3.setUpdateTime(date3);
        billCoinUserPropertyList.add(billCoinUserProperty3);
        BillCoinUserProperty billCoinUserProperty1 = new BillCoinUserProperty();
        billCoinUserProperty1.setId(1L);
        billCoinUserProperty1.setUpdateTime(date1);
        billCoinUserPropertyList.add(billCoinUserProperty1);
        BillCoinUserProperty billCoinUserProperty2 = new BillCoinUserProperty();
        billCoinUserProperty2.setUpdateTime(date2);
        billCoinUserProperty2.setId(2L);
        billCoinUserPropertyList.add(billCoinUserProperty2);
        System.out.println(JSON.toJSONString(billCoinUserPropertyList));
        Date max = billCoinUserPropertyList.stream().map(BillCoinUserProperty::getUpdateTime).max(Comparator.naturalOrder()).orElse(new Date());
        System.out.println(DateUtil.date2str(max));
        if (true) {
            return;
        }
        for (int i = 0; i < 20; i++) {
            //String tableSuffix = SplitTableUtils.getTableNameSuffixForDay(DateUtil.addDay(nowDate, i));
            //System.out.println(tableSuffix);
        }
        if (true) {
            return;
        }
        //System.out.println(DateUtil.getDefaultDate(new Date().getTime()));
        List<CommonBillChangeData> addErrorListToBillResult = new ArrayList<>();
        addErrorListToBillResult.add(CommonBillChangeData.builder().bizId(1L).build());
        addErrorListToBillResult.add(CommonBillChangeData.builder().bizId(2L).build());
        addErrorListToBillResult.add(CommonBillChangeData.builder().bizId(3L).build());
        addErrorListToBillResult.add(CommonBillChangeData.builder().bizId(4L).build());
        addErrorListToBillResult.add(CommonBillChangeData.builder().bizId(5L).build());
        addErrorListToBillResult.add(CommonBillChangeData.builder().bizId(6L).build());
        Iterator<CommonBillChangeData> iterator = addErrorListToBillResult.iterator();
        while (iterator.hasNext()) {
            CommonBillChangeData next = iterator.next();
            //System.out.println("==" + JSON.toJSONString(next));
            iterator.remove();

            List<CommonBillChangeData> eachList = new ArrayList<>();
            eachList.add(next);
            eachList.addAll(addErrorListToBillResult);
            for (CommonBillChangeData commonBillChangeData : addErrorListToBillResult) {
                //System.out.println(JSON.toJSONString(commonBillChangeData));
            }
        }
        List<CommonBillChangeData> eachList = new ArrayList<>();
        eachList.addAll(addErrorListToBillResult);
        FinanceBizTypeEnum bizType = FinanceBizTypeEnum.toEnum("offset_in_ssm");
        // System.out.println(bizType.isPositionProfitType());
        // System.out.println("--------------");


        List<String> ignoreDataCondition = new ArrayList<>();
        ignoreDataCondition.add("#data['type'].equals('9') and #data['notes'].equals('completeOrder')");
        ignoreDataCondition.add("#data['type'].equals('9') and #data['notes'].equals('p2pSaasCompleteOrderFeeSubFreeze')");
        ignoreDataCondition.add("#data['type'].equals('31') and #data['notes'].equals('completeOrder')");
        ignoreDataCondition.add("#data['type'].equals('31') and #data['notes'].equals('p2pSaasCompleteOrderFeeSubFreeze')");
        StringBuilder sb = new StringBuilder("#{");
        for (int i = 0; i < ignoreDataCondition.size(); i++) {
            sb.append(i == 0 ? "" : " or ").append("(").append(ignoreDataCondition.get(i)).append(")");
        }
        sb.append("}");
        String pattern = sb.toString();
        Expression expression = new SpelExpressionParser().parseExpression(pattern, new TemplateParserContext());
        Map<String, String> map = new HashMap<>();
        map.put("type", "31");
        map.put("notes", "p2pSaasCompleteOrderFeeSubFreeze");
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("data", map);
        // System.out.println(expression.getValue(context, Boolean.class));
    }
}
