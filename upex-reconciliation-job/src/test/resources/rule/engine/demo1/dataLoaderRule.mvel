log.info("processFilterRule context:{}",context.toString());
List accountTypes = [10,52,53,54];
List userIds = [*********L, **********L];


log.info("dataLoaderRule execute start");
Map systemUserRelationshipsMap = [
"*********#52":  "52-default",
"**********#52":  "52-default",
"**********#10":  "10-default",
"**********#21":  "21-default",
"**********#22":  "22-default",
"**********#10":  "10-default"
];
log.info("dataLoaderRule execute 初始化");



CommonBillChangeData billFlowData = (CommonBillChangeData) context.get("billFlowData");
log.info("dataLoaderRule execute billFlowData:{}", JSONObject.toJSONString(billFlowData));
// 注释测试
Integer accountType = Integer.parseInt(billFlowData.accountType);
Long accountId = billFlowData.accountId;

if(accountId == null || accountId == empty){
    return false;
}

value = systemUserRelationshipsMap.get(String.valueOf(accountId)+"#"+accountType);

return value != empty && value == accountType + "-default";