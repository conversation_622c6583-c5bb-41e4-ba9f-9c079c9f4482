//log.info("dataCheckerRule context:{}",context.toString());
import com.upex.reconciliation.service.dao.entity.BillBizTypeConfig;

// 注册告警模板
String code = "flowBizTypeNotExistAlarm";
String text = "业务类型不存在告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{}";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);
code = "flowBizInTypeValueAlarm";
text = "入出金类型正负值错误：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{} 类型：{} 金额:{}";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);
code = "decoderAbandonAlarm";
text = "不支持业务线告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{}";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);
code = "flowDemoAccountForbidAlarm";
text = "该类型禁止Demo账户访问告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{} 用户Id:{}";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);
code = "flowSystemAccountAlarm";
text = "该类型只能系统账户访问告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{} 用户Id:{}";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);
code = "flowBizTypeUserIdsAlarm";
text = "该类型只能指定用户访问告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{} 用户Id:{}";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);
code = "flowBizTypeAmountRangeAlarm";
text = "流水金额超过业务类型金额范围告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{} 最小金额:{} 最大金额:{}  流水金额:{} ";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);
code = "illegalBizTypeAccessAlarm";
text = "非法业务类型告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{}";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);
code = "flowPapAccountForbidAlarm";
text = "模拟盘非法业务类型告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{}";
AlarmTemplateEnum.registerTemplate(code, code, text, AlarmTemplateQpsEnum.FIVE_MINUTE);


CommonBillChangeData billFlowData = (CommonBillChangeData) context.get("billFlowData");
Byte accountType = billFlowData.accountType;
String bizType = billFlowData.bizType;
Long bizId = billFlowData.bizId;
Long userId = billFlowData.accountId;
BigDecimal changePropSum = ruleEngineDataService.getChangePropSumByBillChangeData(billFlowData);

// 业务类型是否存在规则校验
Map businessBizTypeConfigMap = ruleEngineDataService.getBusinessBizTypeConfigMap(billFlowData.accountType);
BillBizTypeConfig billBizTypeConfig = (BillBizTypeConfig) businessBizTypeConfigMap.get(billFlowData.bizType);
//log.info("billBizTypeConfig:{}",billBizTypeConfig);
if(billBizTypeConfig == null){
    alarmNotifyService.alarm("flowBizTypeNotExistAlarm", accountType, bizType, bizId);
    return false;
}

// 业务线是否支持校验规则
if(billBizTypeConfig.decoderAbandon == 1){
    alarmNotifyService.alarm("decoderAbandonAlarm", accountType, bizType, bizId);
}

// 流水正负值规则校验开发
List bizInTypeList = ruleEngineDataService.getBizInTypeList(billFlowData.accountType);
if(bizInTypeList != null && bizInTypeList.contains(billFlowData.bizType) && changePropSum.compareTo(BigDecimal.ZERO) < 0){
    alarmNotifyService.alarm("flowBizInTypeValueAlarm",accountType, bizType, bizId ,"入金", changePropSum);
}
List bizOutTypeList = ruleEngineDataService.getBizOutTypeList(billFlowData.accountType);
if(bizOutTypeList != null && bizOutTypeList.contains(billFlowData.bizType) && changePropSum.compareTo(BigDecimal.ZERO) > 0){
    alarmNotifyService.alarm("flowBizInTypeValueAlarm",accountType, bizType, bizId ,"出金", changePropSum);
}

// 非法业务类型告警
Boolean isDemoAccount = ruleEngineDataService.isDemoUser(userId);
if((billBizTypeConfig.demoAccount == 1 && !isDemoAccount) || (billBizTypeConfig.demoAccount == 2 && isDemoAccount)){
    alarmNotifyService.alarm("illegalBizTypeAccessAlarm", accountType, bizType, bizId, userId);
}

// 模拟盘账户
//Boolean isPapAccount = ruleEngineDataService.isPapUser(userId);
//if((billBizTypeConfig.papAccount == 1 && !isPapAccount) || (billBizTypeConfig.demoAccount == 2 && isPapAccount)){
//    alarmNotifyService.alarm("flowPapAccountForbidAlarm", accountType, bizType, bizId, userId);
//}

// 系统账户规则开发
Boolean isSystemAccount = ruleEngineDataService.isSysUser(userId);
if(billBizTypeConfig != null && billBizTypeConfig.getSystemAccount() == 1 && !isSystemAccount){
    alarmNotifyService.alarm("flowSystemAccountAlarm", accountType, bizType, bizId, userId);
}

// 流水指定类型用户规则开发
if(billBizTypeConfig != null && StringUtils.isNotEmpty(billBizTypeConfig.getUserIds()) && billBizTypeConfig.getUserIds().indexOf(userId.toString()) == -1){
    alarmNotifyService.alarm("flowBizTypeUserIdsAlarm",accountType, bizType, bizId, userId);
}

// 流水最大最小金额校验规则开发
BigDecimal changePropSum = ruleEngineDataService.getChangePropSumByBillChangeData(billFlowData);
if(billBizTypeConfig != null && billBizTypeConfig.getMaxAmount().compareTo(BigDecimal.ZERO) > 0 && changePropSum.compareTo(billBizTypeConfig.getMaxAmount()) > 0){
    alarmNotifyService.alarm("flowBizTypeAmountRangeAlarm", accountType, bizType, bizId, billBizTypeConfig.getMaxAmount(), billBizTypeConfig.getMinAmount(), changePropSum);
}
if(billBizTypeConfig != null && billBizTypeConfig.getMinAmount().compareTo(BigDecimal.ZERO) > 0 && changePropSum.compareTo(billBizTypeConfig.getMinAmount()) < 0){
    alarmNotifyService.alarm("flowBizTypeAmountRangeAlarm", accountType, bizType, bizId, billBizTypeConfig.getMaxAmount(), billBizTypeConfig.getMinAmount(), changePropSum);
}

// 流水类型时间范围校验功能开发
//log.info("checkBusnessIllegalBizType end");

return true;