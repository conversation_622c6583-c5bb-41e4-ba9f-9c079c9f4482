Long userId = context.get("userId");
Pair checkPair = context.get("checkPair");
Long codeList = context.get("codeList");
Boolean isSubAccount = false;
// 提现权限
if (codeList.contains(2L)) {
    // 是否子账户
    isSubAccount = ruleEngineDataService.isSubAccount(userId);
}
if (!isSubAccount && checkPair.left) {
    return false;
}
String permissionCode = "";
foreach(code : codeList) {
    if (permissionCode.length() > 0) {
        permissionCode +=",";
    }
    permissionCode += code;
}

// 权限不通过，告警
String accountType = context.get("accountType");
String bizType = context.get("bizType");
String bizMsg = context.get("bizMsg");
Long bizId = context.get("bizId");
// 注册告警模板
String code = "userPermissionAlarm";
// 是否系统用户
Boolean isSystemAccount = ruleEngineDataService.isSysUser(userId);
String text = "用户权限告警：❗❗❗ 用户Id：{} 业务线：({}) 业务类型：{} 是否系统用户：{} 是否子账户：{} 业务信息：{} 权限：{} 校验失败：{} 消息Id：{}";
AlarmTemplateEnum.registerTemplate(code, text);
alarmNotifyService.alarm("userPermissionAlarm", userId, accountType, bizType, isSystemAccount, isSubAccount, bizMsg, permissionCode, checkPair.right, bizId);
return true;