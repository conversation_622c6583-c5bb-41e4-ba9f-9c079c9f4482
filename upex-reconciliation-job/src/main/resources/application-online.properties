spring.application.name=upex-reconciliation-job


##dynamic dataSource
upex.datasource.apollo.namespace=application
upex.datasource.apollo.datasourceConfigList=bill.datasource.config
#
# otc client
#
upex.feignClient.otc=upex-otc
upex.feignClient.spot=upex-spot-rest
upex.feignClient.spot.query=upex-spot-rest-query
upex.feignClient.swap=upex-swap-rest
upex.feignClient.user=upex-user-rest
upex.feignClient.ticker=upex-ticker-rest
upex.feignClient.newwallet=upex-newwallet-rest
upex.feignClient.financial=upex-financial-rest
upex.feignClient.margin.rest=upex-margin-rest
upex.feignClient.margin.rest.api = upex-margin-rest
upex.feignClient.mcprocess=mc-process-rest-online
upex.feignClient.mcprocess.query=mc-process-rest-query
upex.feignClient.payment=upex-fiat-payment-center-rest
upex.feignClient.data.risk=upex-data-risk-rest
upex.feignClient.fiat.order.center=upex-fiat-order-center-rest
upex.feignClient.config=upex-config-rest
upex.feignClient.assets = upex-assets-rest
upex.feignClient.broker = upex-broker-rest
upex.feignClient.act = upex-act-rest
upex.feignClient.dcStatistics=upex-data-statistics-rest
upex.feignClient.assets.core=upex-assets-core-rest
upex.feignClient.uta.rest=upex-unified-account-rest
upex.feignClient.uta.job=upex-unified-account-job
upex.feignClient.data.feature=upex-data-feature-rest
upex.feignClient.convertrest=upex-convert-rest
upex.feignClient.kline=upex-kline-rest
upex.feignClient.mcmatch=upex-mcmatch-rest


#
# xxl
#
xxl.job.accessToken=upex_!*0505aim
xxl.job.admin.addresses=http://internal-upex-xxl-job-admin.upex6.net/xxl-job-admin
#xxl.job.admin.addresses=http://************:30186/xxl-job-admin
#xxl.job.admin.addresses=http://test3-upex-xxl-job-admin.k8s.itssofast.com/xxl-job-admin/
xxl.job.executor.appname=upex-reconciliation-job
xxl.job.executor.ip=
xxl.job.executor.port=8090
xxl.job.executor.logpath=/data/logs/upex-reconciliation-job/jobhandler
xxl.job.executor.logretentiondays=3
#
#redis
#

upex.config.spot.coinInject.enable=true
upex.config.spot.symbolInject.enable=true
# ================================================================
#logging.level.org.springframework.boot.autoconfigure.logging=debug
logging.level.org.springframework.boot.autoconfigure=info
#workid.url=http://upex-gateway.test7.bitget.tools
#
#jwt token
#
# upex.security.jwt.excludePathPatterns=/webjars/**,/customError/**,/error**/**,/favicon.ico,/inner/**,/admin/**,/hs/**,/actuator/**
# upex.security.jwt.secret=TVeaPff6rRB26W8y
# upex.security.jwt.cryptoKey=6FCkHr3vtsNuY722
# upex.security.jwt.expiration=2592000
#
#message
#
spring.messages.basename=i18n/messages
spring.messages.always-use-message-format=true
spring.messages.cache-duration=3600
spring.messages.fallback-to-system-locale=false
#
# feign client
#
#
# spring application name
#
logging.level.com.upex.bill.data=debug
#spring.redis.host=redis
#spring.redis.port=6379
#spring.redis.pool.max-idle=10
#spring.redis.pool.min-idle=5
#spring.redis.pool.max-wait=3000
#spring.redis.timeout=3000
#spring.redis.password=
#spring.redis.lettuce.pool.max-active=32
#spring.redis.lettuce.pool.max-wait=-1
#spring.redis.lettuce.pool.max-idle=8
#spring.redis.lettuce.pool.min-idle=8

#spring.datasource.driverClassName=com.mysql.jdbc.Driver
#spring.datasource.url=******************************************************
#spring.datasource.username=root
#spring.datasource.password=root
#Eureka
#
# eureka server url
#
eureka.server.url=http://${eureka.host:eureka1-2.upex6.net}:${eureka.port:8761}/eureka/
eureka.client.registryFetchIntervalSeconds=5
eureka.client.serviceUrl.defaultZone=http://${eureka.host:eureka1-2.upex6.net}:${eureka.port:8761}/eureka/,http://${eureka.host:eureka1-1.upex6.net}:${eureka.port:8761}/eureka/
eureka.instance.lease-expiration-duration-in-seconds=15
#Eureka Client\u5237\u65B0\u672C\u5730\u7F13\u5B58\u65F6\u95F4\uFF0C\u9ED8\u8BA430\u79D2\uFF0C \u6539\u62105\u79D2
eureka.instance.lease-renewal-interval-in-seconds=5
eureka.instance.instance-id=${spring.cloud.client.ip-address}:${spring.application.name}:${server.port}
eureka.instance.prefer-ip-address=true

upex.recon.kafka.namesrvAddr=upex-bizdatacenter-kafka01.upex6.net:9092,upex-bizdatacenter-kafka01.upex6.net:9092,upex-bizdatacenter-kafka01.upex6.net:9092

management.endpoints.web.exposure.include=health,service-registry,prometheus
management.endpoints.enabled-by-default=true
management.health.redis.enabled=false
management.health.jms.enabled=false
management.health.rabbit.enabled=false
management.health.db.enabled=false
management.health.mongo.enabled=false
management.health.mail.enabled=false
management.health.diskSpace.enabled=false
management.health.solr.enabled=false
management.server.port=8081
management.server.servlet.context-path=/cc
management.endpoints.health.enabled=true
management.endpoints.health.path=/health
management.management.security.enabled=false

# ProtoBuf Redis
upex.user.cache.pbInject.enable=true
protobuf.cluster.redis.host=upex-redis-data-sparse-user-rw.upex6.net
protobuf.cluster.redis.port=6379
protobuf.cluster.redis.timeout=2000
protobuf.cluster.redis.pool.min-idle=10
protobuf.cluster.redis.pool.max-idle=50
protobuf.cluster.redis.pool.max-wait=3000
protobuf.cluster.redis.pool.max-active=100
protobuf.cluster.redis.endpoint=secretsmanager.ap-northeast-1.amazonaws.com
protobuf.cluster.redis.region=ap-northeast-1
protobuf.cluster.redis.secret-name=upex-redis-data-sparse-user/-/ro