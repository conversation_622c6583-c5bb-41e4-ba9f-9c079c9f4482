spring.profiles.active=local
spring.application.name=upex-reconciliation-job
spring.main.allow-bean-definition-overriding=true

app.id=upex-reconciliation
apollo.bootstrap.enabled=true

upex.security.xss.enabled=false
upex.security.csrf.enabled=false

server.port=8080
server.tomcat.max-threads=128
server.tomcat.accept-count=1000
server.tomcat.max-connections=2000
server.tomcat.min-spare-threads=64

# \u8C03\u5EA6\u4E2D\u5FC3\u90E8\u7F72\u8DDF\u5730\u5740\uFF1A\u5982\u8C03\u5EA6\u4E2D\u5FC3\u96C6\u7FA4\u90E8\u7F72\u5B58\u5728\u591A\u4E2A\u5730\u5740\u5219\u7528\u9017\u53F7\u5206\u9694\u3002\u6267\u884C\u5668\u5C06\u4F1A\u4F7F\u7528\u8BE5\u5730\u5740\u8FDB\u884C"\u6267\u884C\u5668\u5FC3\u8DF3\u6CE8\u518C"\u548C"\u4EFB\u52A1\u7ED3\u679C\u56DE\u8C03"
xxl.job.admin.addresses=http://127.0.0.1:8080/xxl-job-admin
# \u6267\u884C\u5668"AppName"\u548C\u5730\u5740\u4FE1\u606F\u914D\u7F6E\uFF1AAppName\u6267\u884C\u5668\u5FC3\u8DF3\u6CE8\u518C\u5206\u7EC4\u4F9D\u636E\uFF1B\u5730\u5740\u4FE1\u606F\u7528\u4E8E"\u8C03\u5EA6\u4E2D\u5FC3\u8BF7\u6C42\u5E76\u89E6\u53D1\u4EFB\u52A1"\u548C"\u6267\u884C\u5668\u6CE8\u518C"\u3002\u6267\u884C\u5668\u9ED8\u8BA4\u7AEF\u53E3\u4E3A9999\uFF0C\u6267\u884C\u5668IP\u9ED8\u8BA4\u4E3A\u7A7A\u8868\u793A\u81EA\u52A8\u83B7\u53D6IP\uFF0C\u591A\u7F51\u5361\u65F6\u53EF\u624B\u52A8\u8BBE\u7F6E\u6307\u5B9AIP\uFF0C\u8BE5IP\u4E0D\u4F1A\u7ED1\u5B9AHost\u4EC5\u4F5C\u4E3A\u901A\u8BAF\u5B9E\u7528\u3002\u5355\u673A\u90E8\u7F72\u591A\u4E2A\u6267\u884C\u5668\u65F6\uFF0C\u6CE8\u610F\u8981\u914D\u7F6E\u4E0D\u540C\u6267\u884C\u5668\u7AEF\u53E3
xxl.job.executor.appname=upex-reconciliation-job
xxl.job.executor.ip=
xxl.job.executor.port=9999
# \u6267\u884C\u5668\u901A\u8BAFTOKEN\uFF0C\u975E\u7A7A\u65F6\u542F\u7528
xxl.job.accessToken=
# \u6267\u884C\u5668\u8FD0\u884C\u65E5\u5FD7\u6587\u4EF6\u5B58\u50A8\u7684\u78C1\u76D8\u4F4D\u7F6E\uFF0C\u9700\u8981\u5BF9\u8BE5\u8DEF\u5F84\u62E5\u6709\u8BFB\u5199\u6743\u9650
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler
# \u6267\u884C\u5668Log\u6587\u4EF6\u5B9A\u671F\u6E05\u7406\u529F\u80FD\uFF0C\u6307\u5B9A\u65E5\u5FD7\u4FDD\u5B58\u5929\u6570\uFF0C\u65E5\u5FD7\u6587\u4EF6\u8FC7\u671F\u81EA\u52A8\u5220\u9664\u3002\u9650\u5236\u81F3\u5C11\u4FDD\u63013\u5929\uFF0C\u5426\u5219\u529F\u80FD\u4E0D\u751F\u6548\uFF1B
xxl.job.executor.logretentiondays=-1

#ribbon
ribbon.ServerListRefreshInterval=20000
ribbon.ReadTimeout=10000
ribbon.ConnectTimeout=30000
ribbon.MaxAutoRetries=0
ribbon.eager-load.enabled=true

#feign
feign.hystrix.enabled=true
feign.httpclient.enabled=true
feign.client.config.default.connectTimeout=2000
feign.client.config.default.readTimeout=90000
feign.httpclient.max-connections=100
hystrix.threadpool.default.coreSize=50
hystrix.command.default.circuitBreaker.requestVolumeThreshold=1000
hystrix.command.default.execution.timeout.enabled=true
hystrix.command.default.execution.isolation.strategy=SEMAPHORE
hystrix.command.default.execution.isolation.semaphore.maxConcurrentRequests=512
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=50000
hystrix.command.default.fallback.isolation.semaphore.maxConcurrentRequests=50

mc.process.feignClientWrapper.enable=true
upex.assets.feignClientWrapper.enable=false
upex.config.system.accountInject.enable=true
loadBalanceFeignConfiguration.adminRewardsServiceRpcCoreClientWrapper.enable=false

upex.serialNoGenerator.enabled=true
mybatis.config-location=classpath:mybatis/mybatis-config.xml
mybatis.mapper-locations=classpath*:/mybatis/mapper/*.xml

#rocketmq
upex.rocketmq.namesrvAddr=rocketmq.test7.bitget.tools
upex.rocketmq.pushBillChangeForReconciliationTopic50=bill_change_for_reconciliation_50
upex.rocketmq.pushBillChangeForReconciliationConsumerGroup=bill_change_for_reconciliation


upex.recon.kafka.namesrvAddr=dev-test-v351-kafka01.bitget.tools:9092,dev-test-v351-kafka02.bitget.tools:9092,dev-test-v351-kafka03.bitget.tools:9092
upex.recon.kafka.pushBillChangeForReconciliationConsumerGroup=upex-reconciliation-test7
upex.recon.kafka.maxPollRecords=1000

