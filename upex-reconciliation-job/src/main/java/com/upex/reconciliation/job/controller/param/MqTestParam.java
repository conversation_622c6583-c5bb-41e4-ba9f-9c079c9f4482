package com.upex.reconciliation.job.controller.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MqTestParam {
    private BigDecimal balanceAvailableChange;
    private BigDecimal beforeBalanceAvailable;
    private long accountId;
    private long id;
    private BigDecimal prop1 = BigDecimal.ZERO;
    private BigDecimal prop2 = BigDecimal.ZERO;
    private String coinId;
    private int bizType;
    private BigDecimal balanceChange;
    private BigDecimal quoteTokenBalanceChange;
    private String tokenId;
    private Date bizTime;
    private String maxOffset;
    private String accountParam;
    private int partitionSize;
    private Long checkTime;
    private Byte accountType;
    private String inAllAccountType;
    private Long limit;

    private Long startTime;
    private Long endTime;
    private Long minId;

    String action;
    String templateCode;
    String templateContent;
    String templateName;
    Integer version;

    String uniqueId;

    private Long sceneId;

    private String tableSchema;
    private String[] tableName;
    private Boolean ledgerReInit = false;

    private String assetsCheckType;
    private String assetsCheckParam;
}
