package com.upex.reconciliation.job.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/05
 **/
public abstract class AbstractDataSourceConfig {
    private static final String CONFIG_LOCATION = "classpath:mybatis/mybatis-config.xml";

    protected static DataSourceTransactionManager createTransactionManager(final DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    protected static SqlSessionFactory createSqlSessionFactory(final DataSource dataSource, final String mapperLocation)
            throws Exception {
        return AbstractDataSourceConfig.createSqlSessionFactory(dataSource, new String[]{mapperLocation});
    }

    private static SqlSessionFactory createSqlSessionFactory(final DataSource dataSource, final String[] mapperLocations)
            throws Exception {
        return AbstractDataSourceConfig.createSqlSessionFactoryBean(dataSource, mapperLocations).getObject();
    }

    protected static SqlSessionFactoryBean createSqlSessionFactoryBean(final DataSource dataSource,
                                                                       final String mapperLocation) throws Exception {
        return AbstractDataSourceConfig.createSqlSessionFactoryBean(dataSource, new String[]{mapperLocation});
    }

    private static SqlSessionFactoryBean createSqlSessionFactoryBean(final DataSource dataSource,
                                                                     final String[] mapperLocations) throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        sessionFactory.setConfigLocation(new PathMatchingResourcePatternResolver().getResource(CONFIG_LOCATION));
        sessionFactory.setMapperLocations(AbstractDataSourceConfig.resolveMapperLocations(mapperLocations));
        return sessionFactory;
    }

    protected static SqlSessionTemplate createSqlSessionTemplate(final SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    protected static TransactionTemplate createTransactionTemplate(final DataSourceTransactionManager transactionManager) {
        return new TransactionTemplate(transactionManager);
    }

    private static Resource[] resolveMapperLocations(final String[] mapperLocations) {
        final ResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
        final List<Resource> resources = new ArrayList<>();
        if (mapperLocations != null) {
            for (final String mapperLocation : mapperLocations) {
                try {
                    final Resource[] mappers = resourceResolver.getResources(mapperLocation);
                    resources.addAll(Arrays.asList(mappers));
                } catch (final IOException e) {
                    // ignore
                }
            }
        }
        return resources.toArray(new Resource[resources.size()]);
    }
}
