package com.upex.reconciliation.job.applicationlistener;

import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @Date 2023/11/27 11:49
 */
@Slf4j
@Component
public class StoppedListener implements ApplicationListener<ContextClosedEvent> {
    @Resource
    private BillEngineManager billEngineManager;

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("bill application stopping ... ");
        try {
            billEngineManager.stop();
            Thread.sleep(3000);
            log.info("bill application kafka stopped ... ");
        } catch (Exception ex) {
            log.error("bill application shutdown kafka error", ex);
        }
    }
}
