package com.upex.reconciliation.job.applicationlistener;

import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @Date 2023/11/27 11:49
 */
@Slf4j
@Component
public class StartedListener implements ApplicationListener<ApplicationReadyEvent> {
    @Resource
    private BillEngineManager billEngineManager;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("bill application starting .... ");
        billEngineManager.start();
        log.info("bill application started springProfilesActive={} springApplicationName={} serviceInstanceName={}",
                System.getProperty("spring.profiles.active"), System.getProperty("spring.application.name"), System.getProperty("service.instance.name"));
    }
}
