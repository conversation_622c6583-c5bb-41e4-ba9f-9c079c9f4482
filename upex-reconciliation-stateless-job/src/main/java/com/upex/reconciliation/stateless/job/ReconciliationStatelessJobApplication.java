package com.upex.reconciliation.stateless.job;


import cn.hutool.extra.spring.EnableSpringUtil;
import com.upex.spot.dto.enums.SpotBillBizTypeEnum;
import com.upex.utils.id.WorkIdClient;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.retry.annotation.EnableRetry;

@Slf4j
@EnableRetry
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class})
@MapperScan("com.upex.reconciliation.service.dao.mapper")
@EnableSpringUtil
public class ReconciliationStatelessJobApplication {

    public static void main(String[] args) throws Exception {
        log.info("upex-reconciliation-job is starting");
        WorkIdClient.init();//不允许catch异常,必须往外抛出
        try {
            log.info("SpotBillBizTypeEnum.BILL_MATCH_FEE_SYS_IN: {}", SpotBillBizTypeEnum.BILL_MATCH_FEE_SYS_IN);
            SpringApplication.run(ReconciliationStatelessJobApplication.class, args);
        } catch (RuntimeException e) {
            log.error("ReconciliationJobApplication Unexpected exception", e);
            throw e;
        }
        log.info("upex-reconciliation-job is finished starting ");
    }
}
