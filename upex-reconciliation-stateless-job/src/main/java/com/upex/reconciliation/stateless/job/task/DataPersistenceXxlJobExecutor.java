package com.upex.reconciliation.stateless.job.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.*;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.createtablebyroute.ReconCreateTableFactory;
import com.upex.reconciliation.service.business.impl.DeleteHistoryDataManagerService;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.model.config.*;
import com.upex.reconciliation.service.model.dto.FixDataParam;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.utils.*;
import com.upex.ticker.facade.dto.PriceVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;
import static com.upex.reconciliation.service.utils.DateUtil.FMT_yyyy_MM_dd_HH_mm_ss;

/**
 * <AUTHOR>
 * @Date 2021/10/9 下午3:58
 * @Description
 */
@Slf4j
@Component
public class DataPersistenceXxlJobExecutor {
    @Resource
    private UserDataPersistenceService userDataPersistenceService;
    @Resource
    private TimeSliceBillCheckService timeSliceBillCheckService;
    @Resource
    private InitDataService initDataService;
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private BillEngineManager billEngineManager;
    @Resource
    private BillCoinUserPropertyErrorService billCoinUserPropertyErrorService;
    @Resource
    private BillUserPositionService billUserPositionService;
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserService;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private BillBizTypeConfigService billBizTypeConfigService;
    @Resource
    private BillContractProfitCoinDetailService billContractProfitCoinDetailService;
    @Resource
    private BillContractProfitSymbolDetailService billContractProfitSymbolDetailService;
    @Resource
    private BillUserPositionTmpService billUserPositionTmpService;
    @Resource
    private BillCoinTypeUserPropertyTmpService billCoinTypeUserPropertyTmpService;
    @Resource
    private BillSymbolTradingConfigService billSymbolTradingConfigService;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;
    @Resource
    private BusinessService businessService;
    @Resource
    private CommonService commonService;
    @Resource
    private DeleteHistoryDataManagerService deleteHistoryDataManagerService;
    @Resource
    private AssetsContractProfitCoinDetailService assetsContractProfitCoinDetailService;
    @Resource
    private BillSymbolCoinPropertyService billSymbolCoinPropertyService;
    @Resource
    private BillWhiteListConfigService billWhiteListConfigService;
    @Resource
    private BillFlowCheckConfigService billFlowCheckConfigService;
    @Resource
    private AssetsContractProfitSymbolDetailService assetsContractProfitSymbolDetailService;
    @Resource
    private BillAlarmLogService billAlarmLogService;
    @Resource
    private BillSymbolPropertyService billSymbolPropertyService;

    @Resource
    private BillUserService billUserService;

    @Resource
    private ReconCreateTableFactory reconCreateTableFactory;

    /**
     * 对账线程
     */
    @XxlJob("baseBillStartByAccountType")
    public void baseBillStartByAccountType() {
        String jobName = "baseBillStartByAccountType";
        String jobParam = XxlJobHelper.getJobParam();
        log.info("baseBillStartByAccountType started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        JSONArray accountTypes = jsonObject.getJSONArray("accountTypes");
        for (int i = 0; i < accountTypes.size(); i++) {
            if (EnvUtil.isRunningAccountType(accountTypes.getByte(i))) {
                try {
                    timeSliceBillCheckService.baseBillStartByAccountType(accountTypes.getByte(i));
                } catch (Exception e) {
                    log.error("baseBillStartByAccountType error accountType:{}", accountTypes.getByte(i), e);
                    alarmNotifyService.alarm(BUSINESS_CHECK_START_ERROR, accountTypes.getByte(i), jobParam);
                }
            } else {
                log.info("baseBillStartByAccountType started is not running:{}", accountTypes.getByte(i));
            }
        }
        redisTemplate.opsForHash().put(RedisUtil.XXL_JOB_EXECUTE_SUCCESS_TIME, "baseBillStartByAccountType", String.valueOf(System.currentTimeMillis()));
        log.info("baseBillStartByAccountType ended");
    }

    /**
     * 数据持久化
     */
    @XxlJob("userDataPersistenceByAccountType")
    public void userDataPersistenceByAccountType() {
        String jobName = "userDataPersistenceByAccountType";
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        JSONArray accountTypes = jsonObject.getJSONArray("accountTypes");
        log.info("userDataPersistenceByAccountType started jobParam:{}", jobParam);

        for (int i = 0; i < accountTypes.size(); i++) {
            if (EnvUtil.isRunningAccountType(accountTypes.getByte(i))) {
                try {
                    userDataPersistenceService.saveTimeSliceDataByAccountType(accountTypes.getByte(i));
                } catch (Exception e) {
                    log.error("userDataPersistenceByAccountType error accountType:{}", accountTypes.getByte(i), e);
                    alarmNotifyService.alarm(DATA_PERSISTENCE_ERROR, accountTypes.getByte(i), jobParam);
                }
            }
        }
        redisTemplate.opsForHash().put(RedisUtil.XXL_JOB_EXECUTE_SUCCESS_TIME, "userDataPersistenceByAccountType", String.valueOf(System.currentTimeMillis()));
        log.info("userDataPersistenceByAccountType ended");
    }

    /**
     * 总账对账
     */
    @XxlJob("ledgerCheckStartByAssetsType")
    public void ledgerCheckStartByAssetsType() {
        String jobName = "ledgerCheckStartByAssetsType";
        String jobParam = XxlJobHelper.getJobParam();
        log.info("ledgerCheckStartByAssetsType started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String assetsType = jsonObject.getString("assetsType");
        String assetsParam = jsonObject.getString("assetsParam");
        try {
            if (EnvUtil.isRunningLedgerAccountType(assetsType, assetsParam)) {
                timeSliceBillCheckService.ledgerCheckStartByAssetsType(assetsType, assetsParam);
                redisTemplate.opsForHash().put(RedisUtil.XXL_JOB_EXECUTE_SUCCESS_TIME, "ledgerCheckStartByAssetsType", String.valueOf(System.currentTimeMillis()));
            }
        } catch (Exception e) {
            log.error("ledgerCheckStartByAssetsType error assetsType:{}", assetsType, e);
            alarmNotifyService.alarm(LEDGER_CHECK_START_ERROR, jobParam);
        }
        log.info("ledgerCheckStartByAssetsType ended");
    }

    /**
     * 总账存盘
     */
    @XxlJob("ledgerSaveStartByAssetsType")
    public void ledgerSaveStartByAssetsType() {
        String jobName = "ledgerSaveStartByAssetsType";
        String jobParam = XxlJobHelper.getJobParam();
        log.info("ledgerSaveStartByAssetsType started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String assetsType = jsonObject.getString("assetsType");
        String assetsParam = jsonObject.getString("assetsParam");
        try {
            if (EnvUtil.isRunningLedgerAccountType(assetsType, assetsParam)) {
                timeSliceBillCheckService.ledgerSaveStartByAssetsType(assetsType, assetsParam);
                redisTemplate.opsForHash().put(RedisUtil.XXL_JOB_EXECUTE_SUCCESS_TIME, "ledgerSaveStartByAssetsType", String.valueOf(System.currentTimeMillis()));
            }
        } catch (Exception e) {
            log.error("ledgerSaveStartByAssetsType error", e);
            alarmNotifyService.alarm(LEVER_SPOT_CHECK_START_ERROR, jobParam);
        }
        log.info("ledgerSaveStartByAssetsType ended");
    }

    /**
     * 总账初始化
     */
    @XxlJob("inAllInit")
    public void inAllInit() {
        String jobName = "inAllInit";
        String jobParam = XxlJobHelper.getJobParam();
        log.info("inAllInit started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Long checkTime = jsonObject.getLong("checkTime");
        String accountType = jsonObject.getString("accountType");
        String accountParam = jsonObject.getString("accountParam");
        try {
            timeSliceBillCheckService.inAllInit(checkTime, accountType, accountParam);
        } catch (Exception e) {
            log.error("inAllInit error", e);
        }
        log.info("inAllInit ended");
    }


    @XxlJob("fixData")
    public void fixData() {
        String jobName = "fixData";
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("fixData started jobParam:{}", jobParam);

        FixDataParam fixData = new FixDataParam();

        fixData.setAccountType(jsonObject.getByte("accountType"));
        fixData.setAccountParam(jsonObject.getString("accountParam"));

        fixData.setUserCoinSnapshotId(jsonObject.getLong("userCoinSnapshotId"));
        fixData.setUserCoinId(jsonObject.getLong("userCoinId"));

        fixData.setUserCoinProp1(jsonObject.getBigDecimal("userCoinProp1"));
        fixData.setUserCoinProp2(jsonObject.getBigDecimal("userCoinProp2"));
        fixData.setUserCoinProp3(jsonObject.getBigDecimal("userCoinProp3"));
        fixData.setUserCoinProp4(jsonObject.getBigDecimal("userCoinProp4"));
        fixData.setUserCoinProp5(jsonObject.getBigDecimal("userCoinProp5"));
        fixData.setLastBizId(jsonObject.getLong("lastBizId"));


        fixData.setBillCoinId(jsonObject.getLong("billCoinId"));

        fixData.setBillCoinProp1(jsonObject.getBigDecimal("billCoinProp1"));
        fixData.setBillCoinProp2(jsonObject.getBigDecimal("billCoinProp2"));
        fixData.setBillCoinProp3(jsonObject.getBigDecimal("billCoinProp3"));
        fixData.setBillCoinProp4(jsonObject.getBigDecimal("billCoinProp4"));
        fixData.setBillCoinProp5(jsonObject.getBigDecimal("billCoinProp5"));

        fixData.setBillCoinTypeId(jsonObject.getLong("billCoinTypeId"));
        fixData.setBillCoinTypeProp1(jsonObject.getBigDecimal("billCoinTypeProp1"));
        fixData.setBillCoinTypeProp2(jsonObject.getBigDecimal("billCoinTypeProp2"));
        fixData.setBillCoinTypeProp3(jsonObject.getBigDecimal("billCoinTypeProp3"));
        fixData.setBillCoinTypeProp4(jsonObject.getBigDecimal("billCoinTypeProp4"));
        fixData.setBillCoinTypeProp5(jsonObject.getBigDecimal("billCoinTypeProp5"));

        try {
            userDataPersistenceService.fixData(fixData);
        } catch (Exception e) {
            log.error("fixData error", e);
        }
        log.info("fixData ended");
    }

    /**
     * 对账数据初始化
     */
    @XxlJob("initReconciliationData")
    public void initReconciliationData() {
        String jobName = "initReconciliationData";
        log.info("XXL job initReconciliationData triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("XXL job initReconciliationData started processing with params {} ,{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Long startTime = jsonObject.getLong("startTime");
        startTime = startTime == null ? System.currentTimeMillis() : startTime;
        Integer accountTypeInt = jsonObject.getIntValue("accountType");
        Integer pageSize = jsonObject.getIntValue("pageSize");
        Integer page = jsonObject.getIntValue("page");
        Integer concurrent = jsonObject.getIntValue("concurrent");
        concurrent = concurrent > 0 ? concurrent : 5;
        String accountParam = jsonObject.getString("accountParam");
        Long maxId = jsonObject.getLong("maxId");
        Long bizTime = startTime / (1000L * 60L) * (1000L * 60L);
        log.info("XXL job initReconciliationData start time round off {}", bizTime);
        try {
            if (EnvUtil.isRunningAccountType(accountTypeInt.byteValue())) {
                initDataService.baseDataInitStart(bizTime, null, accountTypeInt.byteValue(), maxId, pageSize, page, accountParam, concurrent);
            }
        } catch (Exception e) {
            log.error("initReconciliationData failed", e);
        }
        log.info("initReconciliationData ended");
    }

    @XxlJob("rollBackData")
    public void rollBack() {
        log.info("XXL job rollBackData triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        // 业务线回退
        Boolean internalFlag = jsonObject.getBooleanValue("internalFlag");
        Boolean leverSpotFlag = jsonObject.getBooleanValue("leverSpotFlag");
        Boolean businessFlag = jsonObject.getBooleanValue("businessFlag");
        Byte accountType = jsonObject.getByte("accountType");
        Long resetCheckTime = jsonObject.getLong("checkTime");
        Long batchSize = Optional.ofNullable(jsonObject.getLong("batchSize")).orElse(600L);
        Boolean allowOver2Day = jsonObject.getBooleanValue("allowOver2Day");
        log.info("rollBackData started jobParam: {}", jobParam);
        try {
            userDataPersistenceService.rollBackData(internalFlag, leverSpotFlag, businessFlag, accountType, resetCheckTime, batchSize, allowOver2Day);
        } catch (Exception e) {
            log.error("rollBackData error", e);
        }
        log.info("rollBackData ended");
    }

    @XxlJob("fixCoinUserPropertyFromOldBill")
    public void fixCoinUserPropertyFromOldBill() {
        log.info("XXL job fixCoinUserPropertyFromOldBill triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Byte accountType = jsonObject.getByte("accountType");
        String accountParam = jsonObject.getString("accountParam");
        Integer pageSize = jsonObject.getInteger("pageSize");
        Long minId = jsonObject.getLong("minId");

        log.info("fixCoinUserPropertyFromOldBill started jobParam: {}", jobParam);
        try {
            userDataPersistenceService.fixCoinUserPropertyFromOldBill(accountType, accountParam, minId, pageSize);
        } catch (Exception e) {
            log.error("fixCoinUserPropertyFromOldBill error", e);
        }
        log.info("fixCoinUserPropertyFromOldBill ended");
    }


    @XxlJob("cleanData")
    public void cleanData() {
        log.info("XXL job cleanData triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Byte accountType = jsonObject.getByte("accountType");
        String accountParam = jsonObject.getString("accountParam");
        Long deleteSize = jsonObject.getLong("deleteSize");
        log.info("cleanData started jobParam:{}", jobParam);
        try {
            if (EnvUtil.isRunningAccountType(accountType)) {
                userDataPersistenceService.cleanData(accountType, accountParam, deleteSize);
            }
        } catch (Exception e) {
            log.error("cleanData error", e);
        }
        log.info("cleanData ended");
    }

    @XxlJob("restartBillEngineByAccountType")
    public void restartBillEngineByAccountType() {
        log.info("XXL job restartBillEngineByAccountType triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Integer accountTypeInt = jsonObject.getIntValue("accountType");
        log.info("XXL job restartBillEngineByAccountType started processing with params {} ,{}", jobParam, accountTypeInt);
        try {
            if (EnvUtil.isRunningAccountType(accountTypeInt.byteValue())) {
                billEngineManager.restartBillEngine(accountTypeInt.byteValue());
                alarmNotifyService.alarm(accountTypeInt.byteValue(), HOT_RESTART, accountTypeInt);
            }
        } catch (Exception e) {
            log.error("restartBillEngineByAccountType failed accountType:{}", accountTypeInt, e);
        }
        log.info("checkDataByAccountType ended accountType:{}", accountTypeInt);
    }

    @XxlJob("repairUserPropertyErrorData")
    public void repairUserPropertyErrorData() {
        log.info("XXL job repairUserPropertyErrorData triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Byte accountType = jsonObject.getByte("accountType");
        log.info("XXL job repairUserPropertyErrorData started processing with params {} ,{}", jobParam, accountType);
        try {
            if (EnvUtil.isRunningAccountType(accountType)) {
                billCoinUserPropertyErrorService.repairUserPropertyErrorData(jsonObject);
            }
        } catch (Exception e) {
            log.error("repairUserPropertyErrorData failed accountType:{}", accountType, e);
        }
        log.info("repairUserPropertyErrorData ended accountType:{}", accountType);
    }

    @XxlJob("createTable")
    public void createTable() {
        log.info("XXL job createTable triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("XXL job createTable started processing with params {}", jobParam);
        JSONArray userPositionAccountTypes = jsonObject.getJSONArray("userPositionAccountTypes");
        JSONArray coinUserTypeAccountTypes = jsonObject.getJSONArray("coinUserTypeAccountTypes");
        Integer daysInAdvance = jsonObject.getInteger("daysInAdvance");
        try {
            // 创建持仓表
            for (int i = 0; i < userPositionAccountTypes.size(); i++) {
                AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(userPositionAccountTypes.getByte(i));
                billUserPositionService.createTableForDay(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), daysInAdvance != null ? daysInAdvance : 7);
            }
            // 创建币种用户表
            for (int i = 0; i < coinUserTypeAccountTypes.size(); i++) {
                billCoinTypeUserService.createTableForDay(coinUserTypeAccountTypes.getByte(i), daysInAdvance != null ? daysInAdvance : 7);
            }
            // 创建动账表
            billContractProfitTransferService.createTableForMonth(3);
            alarmNotifyService.alarm(CREATE_TABLE_SUCCESS);
            redisTemplate.opsForHash().put(RedisUtil.XXL_JOB_EXECUTE_SUCCESS_TIME, "createTable", String.valueOf(System.currentTimeMillis()));
        } catch (Exception e) {
            log.error("createTable failed", e);
            alarmNotifyService.alarm(CREATE_TABLE_ERROR);
        }
        log.info("createTable ended accountType");
    }

    @XxlJob("createTableByTableRoute")
    public void createTableByTableRoute() {
        log.info("XXL job createTableByTableRoute triggered");
        try {
            reconCreateTableFactory.createTable();
            alarmNotifyService.alarm(CREATE_TABLE_BYROUTE_SUCCESS);
            redisTemplate.opsForHash().put(RedisUtil.XXL_JOB_EXECUTE_SUCCESS_TIME, "createTableByRoute", String.valueOf(System.currentTimeMillis()));
        } catch (Exception e) {
            log.error("createTableByTableRoute failed", e);
            alarmNotifyService.alarm(CREATE_TABLE_BYROUTE_ERROR);
        }
        log.info("createTableByTableRoute ended");

    }

    @XxlJob("cleanUpInactiveUsers")
    @Deprecated
    public void cleanUpInactiveUsers() {
        log.info("XXL job cleanUpInactiveUsers triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("XXL job cleanUpInactiveUsers started processing with params {}", jobParam);
        try {
            List<ApolloReconciliationBizConfig> apolloBizConfigs = ReconciliationApolloConfigUtils.getAllBillConfigByAccountType();
            for (ApolloReconciliationBizConfig apolloBizConfig : apolloBizConfigs) {
                if (apolloBizConfig != null && apolloBizConfig.isOpen() && EnvUtil.isRunningAccountType(apolloBizConfig.getAccountType())) {
                    BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(apolloBizConfig.getAccountType());
                    BillUserCheckModule userCheckModule = (BillUserCheckModule) billLogicGroup.getUserCheckModule();
                    userCheckModule.cleanUpInactiveUsers(apolloBizConfig);
                }
            }
        } catch (Exception e) {
            log.error("cleanUpInactiveUsers failed", e);
            alarmNotifyService.alarm(CLEAN_UP_INACTIVE_USERS_ERROR);
        }
        log.info("cleanUpInactiveUsers ended accountType");
    }

    @XxlJob("deleteHistoryData")
    public void deleteHistoryData() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("deleteHistoryData started jobParam:{}", jobParam);
        try {
            ApolloBillDeleteConfig apolloBillDeleteConfig = ReconciliationApolloConfigUtils.getApolloBillDeleteConfig();
            if (EnvUtil.isRunningServiceInstanceName(apolloBillDeleteConfig.getServiceInstanceName())) {
                deleteHistoryDataManagerService.deleteHistoryData();
                alarmNotifyService.alarm(DELETE_HISTORY_DATA_SUCCESS);
            }
        } catch (Exception e) {
            log.error("deleteHistoryData error", e);
        }
        log.info("deleteHistoryData ended");
    }


    @XxlJob("updateAlarmTemplate")
    public void updateAlarmTemplate() {
        log.info("XXL job updateAlarmTemplate triggered");
        try {
            alarmNotifyService.addOrUpdateAlarmTemplate();
        } catch (Exception e) {
            log.error("updateAlarmTemplate error", e);
        }
        log.info("updateAlarmTemplate ended");
    }

    @XxlJob("initSpotInitUnProfitTransfer")
    public void initSpotInitUnProfitTransfer() {
        log.info("XXL job initSpotInitUnProfitTransfer triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("initSpotInitUnProfitTransfer started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Long checkTime = jsonObject.getLong("checkTime");
        try {
            billCapitalInitPropertyService.initSpotInitUnProfitTransfer(checkTime);
        } catch (Exception e) {
            log.error("initSpotInitUnProfitTransfer error", e);
        }
        log.info("initSpotInitUnProfitTransfer ended");
    }

    /**
     * 修复资金初始化数据
     */
    @XxlJob("repairCapitalInitProperty")
    public void repairCapitalInitProperty() {
        log.info("XXL job repairCapitalInitProperty triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairCapitalInitProperty started jobParam:{}", jobParam);
        try {
            billCapitalInitPropertyService.repairCapitalInitProperty(jobParam);
        } catch (Exception e) {
            log.error("repairCapitalInitProperty error", e);
        }
        log.info("repairCapitalInitProperty ended");
    }

    /**
     * 初始化billUser
     */
    @XxlJob("initBillUser")
    public void initBillUser() {
        log.info("XXL job initBillUser triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("initBillUser started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Long startTime = jsonObject.getLong("startTime");
        startTime = startTime == null ? System.currentTimeMillis() : startTime;
        Integer accountTypeInt = jsonObject.getIntValue("accountType");
        Integer pageSize = jsonObject.getIntValue("pageSize");
        Integer page = jsonObject.getIntValue("page");
        Integer concurrent = jsonObject.getIntValue("concurrent");
        concurrent = concurrent > 0 ? concurrent : 5;
        String accountParam = jsonObject.getString("accountParam");
        Long maxId = jsonObject.getLong("maxId");
        Long bizTime = startTime / (1000L * 60L) * (1000L * 60L);
        try {
            initDataService.initBillUser(bizTime, accountTypeInt.byteValue(), maxId, pageSize, page, accountParam);
        } catch (Exception e) {
            log.error("initBillUser error", e);
        }
        log.info("initBillUser ended");
    }

    /**
     * 初始化billUser
     */
    @XxlJob("incrementInitBillUser")
    public void incrementInitBillUser() {
        log.info("XXL job incrementInitBillUser triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("incrementInitBillUser started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Long startTime = jsonObject.getLong("startTime");
        startTime = startTime == null ? System.currentTimeMillis() : startTime;
        Integer accountTypeInt = jsonObject.getIntValue("accountType");
        Integer pageSize = jsonObject.getIntValue("pageSize");
        Integer page = jsonObject.getIntValue("page");
        String accountParam = jsonObject.getString("accountParam");
        Long maxId = 1L;
        Long bizTime = startTime / (1000L * 60L) * (1000L * 60L);
        try {
            String redisKey = RedisUtil.INCREMENT_INIT_BILL_USER_MAX_ID + accountTypeInt;
            String lastMaxId = (String) redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotEmpty(lastMaxId)) {
                maxId = Long.parseLong(lastMaxId);
            }
            Long syncMaxId = initDataService.initBillUser(bizTime, accountTypeInt.byteValue(), maxId, pageSize, page, accountParam);
            if (syncMaxId != null) {
                redisTemplate.opsForValue().set(redisKey, syncMaxId.toString());
            }
        } catch (Exception e) {
            log.error("incrementInitBillUser error", e);
        }
        log.info("incrementInitBillUser ended");
    }

    @XxlJob("deleteCoinTypeUserProperty")
    public void deleteCoinTypeUserProperty() {
        log.info("XXL job deleteCoinTypeUserProperty triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Byte accountType = jsonObject.getByte("accountType");
        String accountParam = jsonObject.getString("accountParam");
        Long startCheckTime = jsonObject.getLong("startCheckTime");
        Long endCheckTime = jsonObject.getLong("endCheckTime");
        Long deleteSize = jsonObject.getLong("deleteSize");
        log.info("deleteCoinTypeUserProperty started jobParam: {}", jobParam);
        if (accountType == null || StringUtils.isEmpty(accountParam) || startCheckTime == null || endCheckTime == null || deleteSize == null) {
            return;
        }
        try {
            userDataPersistenceService.deleteCoinTypeUserProperty(accountType, accountParam, startCheckTime, endCheckTime, deleteSize);
        } catch (Exception e) {
            log.error("deleteCoinTypeUserProperty error", e);
        }
        log.info("deleteCoinTypeUserProperty ended");
    }

    /**
     * 维护billBizTypeConfig表数据
     */
    @XxlJob("repairBillBizTypeConfig")
    public void repairBillBizTypeConfig() {
        log.info("XXL job repairBillBizTypeConfig triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairBillBizTypeConfig started jobParam:{}", jobParam);
        try {
            billBizTypeConfigService.repairBillBizTypeConfig(jobParam);
        } catch (Exception e) {
            log.error("repairBillBizTypeConfig error", e);
        }
        log.info("repairBillBizTypeConfig ended");
    }

    @XxlJob("rollBackContractProfitData")
    public void rollBackContractProfitData() {
        log.info("XXL job rollBackContractProfitData triggered");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        // 业务线回退
        Byte accountType = jsonObject.getByte("accountType");
        String accountParam = jsonObject.getString("accountParam");
        String rollBackSymbolTable = jsonObject.getString("rollBackSymbolTable");
        // 总账回退
        Long resetCheckTime = jsonObject.getLong("checkTime");
        log.info("rollBackContractProfitData started jobParam: {}", jobParam);
        try {
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
            Long nextSaveTimeSlice = TimeSliceCalcUtils.getNextSaveTimeSlice(resetCheckTime, globalBillConfig.getTimeSliceSize(), globalBillConfig.getMergeTimeSliceSize());
            Date nowDate = new Date();
            while (nextSaveTimeSlice <= nowDate.getTime()) {
                billContractProfitTransferService.deleteByCheckTime(accountType, accountParam, new Date(nextSaveTimeSlice));
                if (AccountTypeEnum.INTERNAL == accountTypeEnum) {
                    long prevSaveTimeSlice = TimeSliceCalcUtils.getPreSaveTimeSlice(nextSaveTimeSlice, globalBillConfig.getTimeSliceSize(), globalBillConfig.getMergeTimeSliceSize());
                    assetsContractProfitCoinDetailService.deleteAfterRecord(new Date(prevSaveTimeSlice), accountTypeEnum.getBizTag(), accountTypeEnum.getAccountParam());
                    assetsContractProfitSymbolDetailService.deleteAfterRecord(new Date(prevSaveTimeSlice), accountTypeEnum.getBizTag(), accountTypeEnum.getAccountParam());
                } else {
                    billContractProfitCoinDetailService.deleteByCheckTime(accountType, accountParam, new Date(nextSaveTimeSlice));
                    if ("true".equalsIgnoreCase(rollBackSymbolTable)) {
                        billContractProfitSymbolDetailService.deleteByCheckTime(accountType, accountParam, new Date(nextSaveTimeSlice));
                    }
                }
                nextSaveTimeSlice = TimeSliceCalcUtils.getNextSaveTimeSlice(nextSaveTimeSlice, globalBillConfig.getTimeSliceSize(), globalBillConfig.getMergeTimeSliceSize());
            }
        } catch (Exception e) {
            log.error("rollBackContractProfitData error", e);
        }
        log.info("rollBackContractProfitData ended");
    }

    /**
     * 维护KafkaIllegalMessageUserIds
     */
    @XxlJob("repairKafkaIllegalMessageUserIds")
    public void repairKafkaIllegalMessageUserIds() {
        log.info("XXL job repairKafkaIllegalMessageUserIds triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairKafkaIllegalMessageUserIds started jobParam:{}", jobParam);
        try {
            billBizTypeConfigService.repairKafkaIllegalMessageUserIds(jobParam);
        } catch (Exception e) {
            log.error("repairKafkaIllegalMessageUserIds error", e);
        }
        log.info("repairKafkaIllegalMessageUserIds ended");
    }

    /**
     * 维护repairCoinTypeUserPropertyTmp
     */
    @XxlJob("repairCoinTypeUserPropertyTmp")
    public void repairCoinTypeUserPropertyTmp() {
        log.info("XXL job repairCoinTypeUserPropertyTmp triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairCoinTypeUserPropertyTmp started jobParam:{}", jobParam);
        try {
            billCoinTypeUserPropertyTmpService.repairCoinTypeUserPropertyTmp(jobParam);
        } catch (Exception e) {
            log.error("repairCoinTypeUserPropertyTmp error", e);
        }
        log.info("repairCoinTypeUserPropertyTmp ended");
    }

    /**
     * 维护repairCoinTypeUserPropertyTmp
     */
    @XxlJob("repairUserPositionTmp")
    public void repairUserPositionTmp() {
        log.info("XXL job repairUserPositionTmp triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairUserPositionTmp started jobParam:{}", jobParam);
        try {
            billUserPositionTmpService.repairUserPositionTmp(jobParam);
        } catch (Exception e) {
            log.error("repairUserPositionTmp error", e);
        }
        log.info("repairUserPositionTmp ended");
    }

    @XxlJob("moveTradingConfigToCapitalInit")
    public void moveTradingConfigToCapitalInit() {
        log.info("XXL job moveTradingConfigToCapitalInit triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("moveTradingConfigToCapitalInit started jobParam:{}", jobParam);
        try {
            billSymbolTradingConfigService.moveTradingConfigToCapitalInit(jobParam);
        } catch (Exception e) {
            log.error("moveTradingConfigToCapitalInit error", e);
        }
        log.info("moveTradingConfigToCapitalInit ended");
    }

    /**
     * 维护repairCoinTypeUserPropertyTmp
     */
    @XxlJob("repairCoinUserProperty")
    public void repairCoinUserProperty() {
        log.info("XXL job repairCoinUserProperty triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairCoinUserProperty started jobParam:{}", jobParam);
        try {
            billCoinUserPropertyService.repairCoinUserProperty(jobParam);
        } catch (Exception e) {
            log.error("repairCoinUserProperty error", e);
        }
        log.info("repairCoinUserProperty ended");
    }

    /**
     * 充值不到账数据初始化
     */
    @XxlJob("failureOrderInit")
    public void failureOrderInit() {
        log.info("XXL job failureOrderInit execute start");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("failureOrderInit started jobParam:{}", jobParam);
        try {
            businessService.failureOrderInit(jobParam);
        } catch (Exception e) {
            log.error("failureOrderInit error", e);
        }
        log.info("failureOrderInit ended");
    }

    /**
     * 充值不到账数据修复
     */
    @XxlJob("failureOrderRepairJob")
    public void failureOrderRepairJob() {
        log.info("failureOrderRepairJob execute start");
        try {
            businessService.failureOrderRepair();
        } catch (Exception e) {
            log.error("failureOrderRepairJob execute error", e);
        }
        log.info("failureOrderRepairJob execute ended");
    }

    @XxlJob("repairReversedCoinUserProperty")
    public void repairReversedCoinUserProperty() {
        log.info("XXL job repairReversedCoinUserProperty triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairReversedCoinUserProperty started jobParam:{}", jobParam);
        try {
            billCoinUserPropertySnapshotService.repairReversedCoinUserProperty(jobParam);
        } catch (Exception e) {
            log.error("repairReversedCoinUserProperty error", e);
        }
        log.info("repairReversedCoinUserProperty ended");
    }


    @XxlJob("transferUserBillCheckResult")
    public void transferUserBillCheckResult() {
        try {
            log.info("transferUseBillCheckResult  start .......");
            //当前时间
            Date currentTime = new Date();
            ApolloProfitTransferConfig transferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
            if (!transferConfig.isOpen()) {
                return;
            }
            if (EnvUtil.isRunningServiceInstanceName(transferConfig.getServiceInstanceName())) {
                billContractProfitTransferService.transferUseBillCheckResult(currentTime);
            }
            // 动账结果对账
            log.info("transferUseBillCheckResult check bill end .......");
        } catch (Exception e) {
            log.error("transferUseBillCheckResult execute Exception:", e);
        }
    }

    @XxlJob("transferUserBillCheckResultByAccountType")
    public void transferUserBillCheckResultByAccountType() {
        try {
            String jobParam = XxlJobHelper.getJobParam();
            log.info("transferUserBillCheckResultByAccountType started jobParam:{}", jobParam);
            JSONObject jsonObject = JSONObject.parseObject(jobParam);
            JSONArray paramArray = jsonObject.getJSONArray("toAccountTypes");
            List<Byte> toAccountTypeList = new ArrayList<>();
            for (int i = 0; i < paramArray.size(); i++) {
                toAccountTypeList.add(paramArray.getByte(i));
            }
            if (CollectionUtils.isEmpty(toAccountTypeList)) {
                return;
            }
            //当前时间
            Date currentTime = new Date();
            ApolloProfitTransferConfig transferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
            if (EnvUtil.isRunningServiceInstanceName(transferConfig.getServiceInstanceName())) {
                billContractProfitTransferService.transferUserBillCheckResultByAccountType(currentTime, toAccountTypeList);
            }
            // 动账结果对账
            log.info("transferUserBillCheckResultByAccountType check bill end .......");
        } catch (Exception e) {
            log.error("transferUserBillCheckResultByAccountType execute Exception:", e);
        }
    }

    /**
     * 动账失败补偿任务
     *
     * <AUTHOR>
     * @date 2023/12/4 11:18
     */
    @XxlJob("transferFailureRepair")
    public void transferFailureRepair() {
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            log.info("transferFailureRepair execute started .......");
            ApolloProfitTransferConfig transferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
            if (!transferConfig.isOpen()) {
                return;
            }
            if (EnvUtil.isRunningServiceInstanceName(transferConfig.getServiceInstanceName())) {
                billContractProfitTransferService.transferFailureRepair();
            }
            log.info("transferFailureRepair execute end .......time:{}", stopwatch.stop());
        } catch (Exception e) {
            log.error("transferFailureRepair execute Exception:", e);
        }
    }

    @XxlJob("querySpotCoinPrice")
    public void querySpotCoinPrice() {
        log.info("XXL job querySpotCoinPrice triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("querySpotCoinPrice started jobParam:{}", jobParam);
        try {
            JSONObject jsonObject = JSONObject.parseObject(jobParam);
            Long checkTime = jsonObject.getLong("checkTime");
            JSONArray coinIds = jsonObject.getJSONArray("coinIds");
            if (checkTime != null) {
                Map<Integer, PriceVo> priceVos = commonService.getCoinIdRatesMapCache(checkTime);
                if (MapUtils.isEmpty(priceVos)) {
                    throw new Exception("未查询到币种价格");
                }
                Map<Integer, PriceVo> logMap = new HashMap<>();
                if (coinIds != null) {
                    for (int i = 0; i < coinIds.size(); i++) {
                        Integer coinId = coinIds.getInteger(i);
                        PriceVo priceVo = priceVos.get(coinId);
                        if (priceVo == null) {
                            throw new Exception("未查询到币种价格");
                        }
                        logMap.put(coinId, priceVo);
                    }
                } else {
                    logMap.putAll(priceVos);
                }
                log.info("DataPersistenceXxlJobExecutor.querySpotCoinPrice priceVos:{}", JSON.toJSONString(logMap));
            }
        } catch (Exception e) {
            log.error("querySpotCoinPrice error", e);
        }
        log.info("querySpotCoinPrice ended");
    }

    @XxlJob("repairSaveTimeSliceRedisStatus")
    public void repairSaveTimeSliceRedisStatus() {
        log.info("XXL job repairSaveTimeSliceRedisStatus triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairSaveTimeSliceRedisStatus started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String accountType = jsonObject.getString("accountType");
        try {
            RedisUtil.getSaveTimeSliceStatusKey(accountType);
            redisTemplate.opsForValue().set(RedisUtil.getSaveTimeSliceStatusKey(accountType), Boolean.TRUE.toString());
        } catch (Exception e) {
            log.error("repairSaveTimeSliceRedisStatus error", e);
        }
        log.info("repairSaveTimeSliceRedisStatus ended");
    }

    /**
     * 总账初始化
     */
    @XxlJob("inAllCoinTypeInit")
    public void inAllCoinTypeInit() {
        String jobName = "inAllCoinTypeInit";
        String jobParam = XxlJobHelper.getJobParam();
        log.info("inAllCoinTypeInit started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Long checkTime = jsonObject.getLong("checkTime");
        String accountType = jsonObject.getString("accountType");
        String accountParam = jsonObject.getString("accountParam");
        try {
            timeSliceBillCheckService.inAllCoinTypeInit(checkTime, accountType, accountParam);
        } catch (Exception e) {
            log.error("inAllCoinTypeInit error", e);
        }
        log.info("inAllCoinTypeInit ended");
    }

    /**
     * 修复资金初始化数据
     */
    @XxlJob("repairBillSymbolCoinProperty")
    public void repairBillSymbolCoinProperty() {
        log.info("XXL job repairBillSymbolCoinProperty triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairBillSymbolCoinProperty started jobParam:{}", jobParam);
        try {
            billSymbolCoinPropertyService.repairBillSymbolCoinProperty(jobParam);
        } catch (Exception e) {
            log.error("repairBillSymbolCoinProperty error", e);
        }
        log.info("repairBillSymbolCoinProperty ended");
    }

    /**
     * 修复资金初始化数据
     */
    @XxlJob("repairAssetsContractProfitCoinDetail")
    public void repairAssetsContractProfitCoinDetail() {
        log.info("XXL job repairAssetsContractProfitCoinDetail triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairAssetsContractProfitCoinDetail started jobParam:{}", jobParam);
        try {
            assetsContractProfitCoinDetailService.repairAssetsContractProfitCoinDetail(jobParam);
        } catch (Exception e) {
            log.error("repairAssetsContractProfitCoinDetail error", e);
        }
        log.info("repairAssetsContractProfitCoinDetail ended");
    }

    /**
     * 维护billWhiteListConfig表数据
     */
    @XxlJob("repairBillWhiteListConfig")
    public void repairBillWhiteListConfig() {
        log.info("XXL job billWhiteListConfig triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("billWhiteListConfig started jobParam:{}", jobParam);
        try {
            billWhiteListConfigService.repairBillWhiteListConfig(jobParam);
        } catch (Exception e) {
            log.error("billWhiteListConfig error", e);
        }
        log.info("billWhiteListConfig ended");
    }

    /**
     * 维护billFlowCheckConfig表数据
     */
    @XxlJob("repairBillFlowCheckConfig")
    public void repairBillFlowCheckConfig() {
        log.info("XXL job repairBillFlowCheckConfig triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairBillFlowCheckConfig started jobParam:{}", jobParam);
        try {
            billFlowCheckConfigService.repairBillFlowCheckConfig(jobParam);
        } catch (Exception e) {
            log.error("repairBillFlowCheckConfig error", e);
        }
        log.info("repairBillFlowCheckConfig ended");
    }

    /**
     * 删除历史告警日志
     */
    @XxlJob("deleteHistoryAlarmLog")
    public void deleteHistoryAlarmLog() {
        log.info("XXL job deleteHistoryAlarmLog triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("deleteHistoryAlarmLog started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String deleteTime = jsonObject.getString("deleteTime");
        try {
            Date date = DateUtil.str2date(deleteTime, FMT_yyyy_MM_dd_HH_mm_ss);
            if (DateUtil.getGapDays(date, new Date()) < 30) {
                return;
            }
            billAlarmLogService.deleteByTime(date);
        } catch (Exception e) {
            log.error("deleteHistoryAlarmLog error", e);
        }
        log.info("deleteHistoryAlarmLog ended");
    }



    /**
     * 修复重算盈亏对账数据 清除symbol，symbolCoin，position均价数据
     * 需要关闭对账逻辑
     */
    @XxlJob("repairRecalculatePositionProfit")
    public void repairRecalculatePositionProfit() {
        log.info("XXL job repairRecalculatePositionProfit triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairRecalculatePositionProfit started jobParam:{}", jobParam);
        try {
            billSymbolPropertyService.repairRecalculatePositionProfit(jobParam);
        } catch (Exception e) {
            log.error("repairRecalculatePositionProfit error", e);
        }
        log.info("repairRecalculatePositionProfit ended");
    }
    @XxlJob("repairBillUser")
    public void repairBillUser() {
        log.info("XXL job repairBillUser triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairBillUser started jobParam:{}", jobParam);
        try {
            JSONObject jsonObject = JSONObject.parseObject(jobParam);
            Integer pageSize = jsonObject.getInteger("pageSize");
            Byte accountType = jsonObject.getByte("accountType");
            String accountParam = jsonObject.getString("accountParam");
            billUserService.repairBillUser(pageSize, accountType, accountParam);
        } catch (Exception e) {
            log.error("repairBillUser error", e);
        }

    }


    /**
     * 用户盈亏落盘
     */
    @XxlJob("userProfitSaveStartByAccountType")
    public void userProfitSaveStartByAccountType() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("userProfitSaveStartByAccountType started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        JSONArray accountTypes = jsonObject.getJSONArray("accountTypes");
        for (int i = 0; i < accountTypes.size(); i++) {
            Byte accountType = accountTypes.getByte(i);
            String consumerKey = KafkaTopicEnum.RECON_SYNC_BUSINESS_BILL_FLOW_TOPIC.getConsumerName() + "-" + accountType;
            KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(consumerKey);
            if (kafkaConsumerConfig == null) {
                continue;
            }
            if (!EnvUtil.isRunningKafkaConsumer(consumerKey) || !kafkaConsumerConfig.getIsOpen()) {
                log.info("userProfitSaveStartByAccountType start not running instance {} consumerName:{}  kafkaConsumerConfig:{}", EnvUtil.getServiceInstanceName(), consumerKey, JSONObject.toJSONString(kafkaConsumerConfig));
                continue;
            }
            try {
                billEngineManager.getUserProfitCheckModule(accountType).recalculateAndSaveUserProfit();
            } catch (Exception e) {
                log.error("userProfitSaveStartByAccountType error accountType:{}", accountTypes.getByte(i), e);
            }
        }
        redisTemplate.opsForHash().put(RedisUtil.XXL_JOB_EXECUTE_SUCCESS_TIME, "userProfitSaveStartByAccountType", String.valueOf(System.currentTimeMillis()));
        log.info("userProfitSaveStartByAccountType ended");
    }

    /**
     * 用户盈亏清理内存
     */
    @XxlJob("userProfitClearStartByAccountType")
    public void userProfitClearStartByAccountType() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("userProfitClearStartByAccountType started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        JSONArray accountTypes = jsonObject.getJSONArray("accountTypes");
        for (int i = 0; i < accountTypes.size(); i++) {
            Byte accountType = accountTypes.getByte(i);
            String consumerKey = KafkaTopicEnum.RECON_SYNC_BUSINESS_BILL_FLOW_TOPIC.getConsumerName() + "-" + accountType;
            KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(consumerKey);
            if (kafkaConsumerConfig == null) {
                continue;
            }
            if (!EnvUtil.isRunningKafkaConsumer(consumerKey) || !kafkaConsumerConfig.getIsOpen()) {
                log.info("userProfitClearStartByAccountType start not running instance {} consumerName:{}  kafkaConsumerConfig:{}", EnvUtil.getServiceInstanceName(), consumerKey, JSONObject.toJSONString(kafkaConsumerConfig));
                continue;
            }
            try {
                billEngineManager.getUserProfitCheckModule(accountType).clearMemoryUserProfit();
            } catch (Exception e) {
                log.error("userProfitClearStartByAccountType error accountType:{}", accountTypes.getByte(i), e);
            }
        }
        log.info("userProfitClearStartByAccountType ended");
    }

    /**
     * 用户盈亏回测
     */
    @XxlJob("userProfitStartByAccountTypeAndUser")
    public void userProfitStartByAccountTypeAndUser() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("userProfitStartByAccountTypeAndUser started jobParam:{}", jobParam);
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        JSONArray accountTypes = jsonObject.getJSONArray("accountTypes");
        Long userId = jsonObject.getLong("userId");
        Long startTime = jsonObject.getLong("startTime");
        Long endTime = jsonObject.getLong("endTime");
        for (int i = 0; i < accountTypes.size(); i++) {
            Byte accountType = accountTypes.getByte(i);
            String consumerKey = KafkaTopicEnum.RECON_SYNC_BUSINESS_BILL_FLOW_TOPIC.getConsumerName() + "-" + accountType;
            KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(consumerKey);
            if (kafkaConsumerConfig == null) {
                continue;
            }
            if (!EnvUtil.isRunningKafkaConsumer(consumerKey) || !kafkaConsumerConfig.getIsOpen()) {
                log.info("userProfitStartByAccountTypeAndUser start not running instance {} consumerName:{}  kafkaConsumerConfig:{}", EnvUtil.getServiceInstanceName(), consumerKey, JSONObject.toJSONString(kafkaConsumerConfig));
                continue;
            }
            try {
                billEngineManager.getUserProfitCheckModule(accountType).recalculateUserProfit(userId, startTime, endTime);
            } catch (Exception e) {
                log.error("userProfitStartByAccountTypeAndUser error accountType:{}", accountTypes.getByte(i), e);
            }
        }
        log.info("userProfitStartByAccountTypeAndUser ended");
    }
    @XxlJob("repairCoinUserPropertySprop")
    public void repairCoinUserPropertySprop() {
        log.info("XXL job repairCoinUserPropertySprop triggered");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("repairCoinUserPropertySprop started jobParam:{}", jobParam);
        try {
            billCoinUserPropertyService.repairCoinUserPropertySprop(jobParam);
        } catch (Exception e) {
            log.error("repairCoinUserPropertySprop error", e);
        }
        log.info("repairCoinUserPropertySprop ended");
    }
}
