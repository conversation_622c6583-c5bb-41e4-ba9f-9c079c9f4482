<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.AlarmNotifyTemplateMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.AlarmNotifyTemplate">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="template_code" property="templateCode" jdbcType="VARCHAR"/>
        <result column="template_name" property="templateName" jdbcType="VARCHAR"/>
        <result column="template_content" property="templateContent" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="use_default_at_user" property="useDefaultAtUser" jdbcType="INTEGER"/>
        <result column="support_additional_at_user" property="supportAdditionalAtUser" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        template_code,
        template_name,
        template_content,
        version,
        use_default_at_user,
        support_additional_at_user,
        create_time,
        update_time
    </sql>

    <sql id="Base_Column_List_No_Id">
        template_code,
        template_name,
        template_content,
        version,
        use_default_at_user,
        support_additional_at_user,
        create_time,
        update_time
    </sql>

    <sql id="Base_Column_List_Value">
        #{record.templateCode},#{record.templateName},#{record.templateContent},#{record.version},
        #{record.useDefaultAtUser},#{record.supportAdditionalAtUser},
        #{record.createTime},#{record.updateTime}
    </sql>


    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into
        alarm_notify_template
        (<include refid="Base_Column_List_No_Id"/>)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"/>
            </trim>
        </foreach>
    </insert>


    <update id="updateTemplateContentByCode">
        update alarm_notify_template
        set template_content = #{templateContent},
        <if test="templateName != null">
            template_name = #{templateName},
        </if>
        update_time = #{updateTime}
        where template_code = #{templateCode}
    </update>

    <select id="queryTemplateByCode" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from alarm_notify_template
        where template_code = #{templateCode} limit 1
    </select>
</mapper>

