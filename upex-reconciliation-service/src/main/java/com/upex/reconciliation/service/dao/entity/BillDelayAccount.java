package com.upex.reconciliation.service.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 延时入账监控数据表
 * @TableName bill_delay_account
 */
@Data
public class BillDelayAccount implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 对账成功时间
     */
    private Date checkOkTime;

    /**
     * 业务线
     */
    private Integer businessLine;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 业务唯一id
     */
    private String bizId;

    /**
     * 币种
     */
    private String symbol;

    /**
     * 
     */
    private BigDecimal amount;

    /**
     * 业务时间
     */
    private Date bizTime;

    /**
     * 扩展字段
     */
    private String dataExt;

    /**
     * 有效数据标识 0-无 1-有
     */
    private Integer activeFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}