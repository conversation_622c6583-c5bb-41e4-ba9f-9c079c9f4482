package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.OrderBillConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository("orderBillConfigMapper")
public interface OrderBillConfigMapper {

    OrderBillConfig getOrderBillConfig(@Param("orderType") String orderType, @Param("orderParam") String orderParam);

    int insert(@Param("record") OrderBillConfig orderBillConfig);

    boolean updateOrderBillConfig(OrderBillConfig orderBillConfig);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteById(@Param("id") Long id);
}
