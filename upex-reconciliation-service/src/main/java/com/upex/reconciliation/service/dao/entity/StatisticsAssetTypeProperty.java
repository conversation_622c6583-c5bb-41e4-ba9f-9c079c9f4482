package com.upex.reconciliation.service.dao.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/10/9 下午3:21
 * @Description
 */
@Data
public class StatisticsAssetTypeProperty {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 资产类型：1-平台资产 2-系统账户资产 3-客户资产 4-系统客户资产差额 5-钱包资产 6-虚增资产 7-挪用资产
     */
    private Integer assetType;
    /**
     * 快照时间
     */
    private Date snapshotTime;
    /**
     * 资产总额-usdt
     */
    private BigDecimal totalBalance;

    /**
     * 创建时间
     */
    private Date createTime;

}
