package com.upex.reconciliation.service.dao.mapper;


import com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError;
import com.upex.reconciliation.service.model.dto.MaxMinIdDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 乱序数据
 *
 * <AUTHOR>
 */
@Repository("billCoinUserPropertyErrorMapper")
public interface BillCoinUserPropertyErrorMapper {
    /**
     * 批量插入
     *
     * @param records
     * @return
     */
    int batchInsert(@Param("records") List<BillCoinUserPropertyError> records,
                    @Param("accountType") Byte accountType,
                    @Param("accountParam") String accountParam);

    /**
     * 查询乱序资产
     *
     * @return
     */
    BillCoinUserPropertyError selectByUserCoinAndBizId(@Param("userId") Long userId,
                                                       @Param("coinId") Integer coinId,
                                                       @Param("lastBizId") Long lastBizId,
                                                       @Param("accountType") Byte accountType,
                                                       @Param("accountParam") String accountParam);

    BillCoinUserPropertyError selectLatestByUser(@Param("userId") Long userId,
                                                 @Param("accountType") Byte accountType,
                                                 @Param("accountParam") String accountParam);


    /**
     * 批量删除
     *
     * @param beginId
     * @param pageSize
     * @param accountType
     * @param accountParam
     * @return
     */
    Boolean batchDelete(@Param("beginId") Long beginId,
                        @Param("pageSize") Long pageSize,
                        @Param("accountType") Byte accountType,
                        @Param("accountParam") String accountParam);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    Boolean deleteByCheckTime(@Param("accountType") Byte accountType,
                              @Param("accountParam") String accountParam,
                              @Param("startTime") Date startTime,
                              @Param("checkTime") Date checkTime);


    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param id
     * @return
     */
    int deleteById(@Param("accountType") Byte accountType,
                   @Param("accountParam") String accountParam,
                   @Param("id") Long id);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param batchSize
     * @return
     */
    boolean deleteByLtCheckTime(@Param("accountType") Byte accountType,
                                @Param("accountParam") String accountParam,
                                @Param("checkTime") Date checkTime,
                                @Param("batchSize") Long batchSize);

    /**
     * 获取最大最小id
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    MaxMinIdDTO selectMaxMinId(@Param("accountType") Byte accountType,
                               @Param("accountParam") String accountParam);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param startId
     * @param endId
     * @return
     */
    List<BillCoinUserPropertyError> getByStartIdAndEndId(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("startId") Long startId,
            @Param("endId") Long endId);
}