package com.upex.reconciliation.service.model.config;

import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 财务收入对账配置
 */
@Data
public class IncomeCheckUserConfig {


    private String name;

    /**
     * 1、手续费处理（需要考虑待动账）
     * 2、按照biz_type处理
     */
    private Integer type;

    /**
     * 优先按单条业务线走
     */
    private Byte accountType;

    /**
     * 杠杆之类的业务线，会有多条的情况
     */
    private Byte extraAccountType;

    private String accountParam;

    private Long systemUserId;

    private Set<Integer> notFeeCoinIds;

    private List<String> feeBizTypes;

    /**
     * 容差值
     */
    private Map<Integer, BigDecimal> toleranceMap = new HashMap<>();



}
