package com.upex.reconciliation.service.model.alarm;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/8/27 下午3:21
 * @Description
 */
@Slf4j
@Data
public class CheckResultInfo {
    /***交易对***/
    private String symbolId;
    /***币id***/
    private Integer coinId;
    /***已实现***/
    private BigDecimal realized = BigDecimal.ZERO;
    /***未实现***/
    private BigDecimal unrealized = BigDecimal.ZERO;
    /***未实现 + 已实现 + init = 0(理想情况下为0，但是由于数据波动、汇率等因素存在一定容忍值)***/
    private BigDecimal result = BigDecimal.ZERO;
    /***初始值***/
    private BigDecimal init = BigDecimal.ZERO;
    /***实际需要的初始值***/
    private BigDecimal wantedInit = BigDecimal.ZERO;
    /***容忍值***/
    private BigDecimal toleranceValue = BigDecimal.ZERO;
    /***result/init***/
    private BigDecimal proportion = BigDecimal.ZERO;
    /***是否检测通过***/
    private boolean checked = false;
    /***多仓持仓数量***/
    private BigDecimal lCount = BigDecimal.ZERO;
    /***多仓持仓均价***/
    private BigDecimal lAvg = BigDecimal.ZERO;
    /***空仓持仓数量***/
    private BigDecimal sCount = BigDecimal.ZERO;
    /***空仓持仓均价***/
    private BigDecimal sAvg = BigDecimal.ZERO;
    /***检查时间***/
    private Date checkOkTime;
    /***当前价格***/
    private BigDecimal mPrice;
    /***当期已实现***/
    private BigDecimal currentRealized = BigDecimal.ZERO;

    /**
     * 检测 未实现 + 已实现 + init = x ； x在容忍值范围
     */
    public CheckResultInfo checkTradingOnInfo(BigDecimal unrealized, BigDecimal realized, BigDecimal init) {
        return null;
    }
}
