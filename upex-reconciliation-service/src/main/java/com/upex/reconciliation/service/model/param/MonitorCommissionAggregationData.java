package com.upex.reconciliation.service.model.param;

import com.upex.reconciliation.service.common.constants.BillConstants;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2024/5/16 17:23
 */
@Data
public class MonitorCommissionAggregationData extends BillMonitorData {

    private String sceneName;

    private BigDecimal commissionCountChange = BigDecimal.ZERO;

    private BigDecimal feeCountChange = BigDecimal.ZERO;

    private BigDecimal minTolerance = BigDecimal.ZERO;

    private BigDecimal maxTolerance = BigDecimal.ZERO;

    public boolean doCheck() {
        BigDecimal factor = BigDecimal.ONE;
        if ((commissionCountChange != null && commissionCountChange.compareTo(BigDecimal.ZERO) == 0)
                && (feeCountChange != null && feeCountChange.compareTo(BigDecimal.ZERO) == 0)) {
            // 手续费很成交额都为0，即分子分母都为0，直接返回true
            return true;
        }
        if (feeCountChange != null && feeCountChange.compareTo(BigDecimal.ZERO) != 0) {
            factor = commissionCountChange.divide(feeCountChange, BillConstants.SERVER_DEFAULT_SCALE, RoundingMode.HALF_UP).abs();
        }
        return factor.compareTo(minTolerance) >= 0 && factor.compareTo(maxTolerance) <= 0;
    }
}
