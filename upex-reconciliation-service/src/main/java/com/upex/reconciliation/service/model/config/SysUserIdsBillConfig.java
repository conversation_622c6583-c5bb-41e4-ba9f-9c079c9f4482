package com.upex.reconciliation.service.model.config;

import lombok.Data;

import java.util.List;

@Data
public class SysUserIdsBillConfig {

    /**
     * 链上总入金
     */
    private List<String> totalIn;

    /**
     * 链上总出金
     */
    private List<String> totalOut;

    /**
     * 内部转账入金
     */
    private List<String> internalIn;

    /**
     * 内部转账出金
     */
    private List<String> internalOut;

    /**
     * otc 总入金
     */
    private List<String> otcIn;

    /**
     * otc 总出金
     */
    private List<String> otcOut;

    /**
     * 其它收入
     */
    private List<String> other;

    /**
     * USDT合约,表后缀名
     */
    private List<String> uAccountType;

    /**
     * 币本位合约,表后缀名
     */
    private List<String> bAccountType;

    /**
     * 币币交易收入
     */
    private List<String> income;

    /**
     * 币币交易支出
     */
    private List<String> spending;

    /**
     * 币币交易买入
     */
    private List<String> buy;
    /**
     * 币币交易卖出
     */
    private List<String> sell;
    /**
     * 手续费
     */
    private List<String> poundage;

    /**
     * 合约已实现盈亏
     */
    private List<String> profitAndLoss;

    /**
     * 合约资金费率
     */
    private List<String> fundsRate;

    /**
     * 合约体验金
     */
    private List<String> experienceOfGold;

    /**
     * 系统资产用户列表
     */
    private List<Long> sysUserIds;

    /**
     * 子系统列表
     */
    private List<String> subSystemList;

    /**
     * 挪用资产
     */
    private List<AppropriateAssetsModel> appropriateAssetsList;

}
