package com.upex.reconciliation.service.model.dto;

import com.upex.utils.bench.SequenceBenchMarker;
import com.upex.utils.task.function.FunctionP0;
import com.upex.utils.task.function.VoidFunctionP0;
import io.micrometer.core.instrument.DistributionSummary;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.FormattingTuple;
import org.slf4j.helpers.MessageFormatter;

import java.util.LinkedList;
import java.util.List;


@Slf4j
public class MessageMonitor {

    @Getter
    private final String body;

    @Getter
    private final String msgId;

    @Getter
    private final long storeTime;

    @Getter
    private final String tags;

    private final long groupId;

    /**
     * 消息组全局marker
     */
    private final SequenceBenchMarker marker;

    private final boolean activeLog;

    private final String topic;

    private final int queueId;

    private final long singleSlowTime;

    @Getter
    private Byte accountType;

    @Getter
    private String maxOffset;

    private final DistributionSummary producerToMQSummary;

    public MessageMonitor(String body,
                          String maxOffset,
                          byte accountType,
                          String msgId,
                          long storeTime,
                          SequenceBenchMarker marker,
                          boolean activeLog,
                          String topic,
                          int queueId,
                          long groupId,
                          String tags,
                          long singleSlowTime,
                          DistributionSummary producerToMQSummary) {
        this.body = body;
        this.maxOffset=maxOffset;
        this.accountType=accountType;
        this.msgId = msgId;
        this.storeTime = storeTime;
        this.marker = marker;
        this.activeLog = activeLog;
        this.groupId = groupId;
        this.topic = topic;
        this.queueId = queueId;
        this.tags = tags;
        this.singleSlowTime = singleSlowTime;
        this.producerToMQSummary = producerToMQSummary;
    }

    private List<StringBuilder> sbs;

    private List<RecordModel> recordModels;

    public <T> T runInLog(String flag, FunctionP0<T> runner) {
        return marker.runInLog("[MQ_MSG_" + msgId + "]:" + flag, runner);
    }

    public void runInLog(String flag, VoidFunctionP0 runner) {
        marker.runInLog("[MQ_MSG_" + msgId + "]:" + flag, runner);
    }

    /**
     * 普通日志 非日志模式不会开启
     */
    public void log(IMsg iMsg, String appendLogPattern, Object... appendLogArguments) {
        if (!activeLog) {
            return;
        }
        FormattingTuple ft = MessageFormatter.arrayFormat(appendLogPattern, appendLogArguments);
        String appendLog = ft.getMessage();
        StringBuilder sb;
        if (iMsg != null) {
            sb = logDetail(appendLog, iMsg.getMsgCreateTime(), iMsg.getBizId(), false);
        } else {
            sb = logDetail(appendLog, null, null, false);
        }
        if (null != sb) {
            if (null == sbs) {
                sbs = new LinkedList<>();
            }
            sbs.add(sb);
        }
    }

    public void log(String appendLogPattern, Object... appendLogArguments) {
        log(null, appendLogPattern, appendLogArguments);
    }

    /**
     * 异常日志 无需开启日志配置
     */
    public void error(IMsg iMsg, String appendLogPattern, Object... appendLogArguments) {
        FormattingTuple ft = MessageFormatter.arrayFormat(appendLogPattern, appendLogArguments);
        String appendLog = ft.getMessage();
        StringBuilder sb;
        if (null != iMsg) {
            sb = logDetail(appendLog, iMsg.getMsgCreateTime(), iMsg.getBizId(), false);
        } else {
            sb = logDetail(appendLog, null, null, false);
        }
        if (null != sb) {
            if (null == sbs) {
                sbs = new LinkedList<>();
            }
            sbs.add(sb);
        }
    }

    public void error(String appendLogPattern, Object... appendLogArguments) {
        error(null, appendLogPattern, appendLogArguments);
    }

    private StringBuilder logDetail(String appendLog, Long createTime, Long bizId, boolean recordSummary) {
        long ftime = marker.getFirstTime();
        long now = System.currentTimeMillis();
        if ((now - ftime) < singleSlowTime) {
            if (null != bizId && null != createTime) {
                long pTime = storeTime - createTime;
                if (recordSummary) {
                    producerToMQSummary.record(pTime);
                }
            }
            return null;
        }
        StringBuilder sb = new StringBuilder("[consumer_log_").append(groupId)
                .append("]topic=").append(topic)
                .append("|queueId=").append(queueId)
                .append("|msgId=").append(msgId);
        if (null != bizId) {
            sb.append("|bizId=").append(bizId);
            if (null != createTime) {
                long pTime = storeTime - createTime;
                if (recordSummary) {
                    producerToMQSummary.record(pTime);
                }
                sb.append("|totalTime=").append(now - createTime)
                        .append("|PTime=").append(pTime);
            }
        }
        sb.append("|CVTime=").append(marker.getFirstTime() - storeTime)    // 流转耗时 存储->消费开始
                .append("|CTime=").append(now - marker.getFirstTime());
        if (StringUtils.isNotBlank(appendLog)) {
            sb.append(":::").append(appendLog);
        }
        return sb;
    }

    /**
     * 记录IMsg对象，并在最后触发
     */
    public void record(IMsg iMsg) {
        if (!activeLog) {
            return;
        }
        if (null == recordModels) {
            recordModels = new LinkedList<>();
        }
        recordModels.add(new RecordModel(iMsg.getMsgCreateTime(), iMsg.getBizId()));
    }

    /**
     * 打印消费超时的
     */
    public void printResults() {
        if (CollectionUtils.isNotEmpty(sbs)) {
            for (StringBuilder sb : sbs) {
                log.info(sb.toString());
            }
        }
        if (CollectionUtils.isNotEmpty(recordModels)) {
            for (RecordModel model : recordModels) {
                StringBuilder sb = logDetail("consumer_single_data", model.getMsgCreateTime(), model.getBizId(), true);
                if (null != sb) {
                    log.info(sb.toString());
                }
            }
        }
    }


    @Getter
    private static class RecordModel implements IMsg {

        public RecordModel(Long msgCreateTime, Long bizId) {
            this.msgCreateTime = msgCreateTime;
            this.bizId = bizId;
        }

        private final Long msgCreateTime;

        private final Long bizId;

    }

}
