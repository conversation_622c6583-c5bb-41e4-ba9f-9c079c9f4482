package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.reconciliation.service.model.domain.PageIdResponse;
import com.upex.reconciliation.service.model.domain.QueryUserDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("billCoinUserAssetsMapper")
public interface BillCoinUserAssetsMapper {
    int batchInsert(@Param("list") List<BillCoinUserAssets> list,
                     @Param("accountType") Byte accountType,
                     @Param("accountParam") String accountParam);

    /**
     * 查询最后一次checkTime
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    Date selectLastCheckTime(@Param("accountType") Byte accountType,
                             @Param("accountParam") String accountParam);



    int updateCheckTimeById(@Param("ids") List<Long> ids,
                            @Param("checkTime") Date checkTime, @Param("updateTime") Date updateTime,
                            @Param("accountType") Byte accountType,
                            @Param("accountParam") String accountParam);

    List<BillCoinUserAssets> selectSysAssetsSnapShotByTime(@Param("userId") Long userId,
                                                           @Param("coinId") Integer coinId, @Param("requestTime") Date requestTime,
                                                           @Param("accountType") Integer accountType,
                                                           @Param("accountParam") String accountParam,
                                                           Boolean isMaster);


    /**
     * 查询某一个对账时间的所有记录
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    List<BillCoinUserAssets> selectAllAssetsTiming(@Param("accountType") Integer accountType,
                                                   @Param("accountParam") String accountParam,
                                                   @Param("checkTime") String checkTime);

    /**
     * 查询某一个对账时间的所有记录
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    List<BillCoinUserAssets> selectAllAssetsTimingSwapMain(@Param("accountType") Integer accountType,
                                                   @Param("accountParam") String accountParam,
                                                   @Param("checkTime") String checkTime);

    BillCoinUserAssets selectTime(@Param("accountType") Integer accountType,
                                  @Param("accountParam") String accountParam,
                                  @Param("userId") Long userId,
                                  @Param("checkOkTime") Date checkOkTime,
                                  Boolean isMaster);


    /**
     * 删除初始化时间在重置对账时间之后的记录
     *
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int deleteInitialTimeAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                                     @Param("accountType") Integer accountType,
                                     @Param("accountParam") String accountParam);

    /**
     * 更新重置对账时间在初始化时间和最后对账时间之间的记录
     *
     * @param lastCheckTime
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int updateInitAndCheckBetweenRecord(@Param("lastCheckTime") Date lastCheckTime, @Param("resetCheckTime") Date resetCheckTime,
                                        @Param("accountType") Integer accountType,
                                        @Param("accountParam") String accountParam);


    /**
     * 判断time区间，获取单用户prop1
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkOkTime
     * @return
     */
    List<BillCoinUserAssets> selectSingleByParamAndTime(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkOkTime,
            Boolean isMaster);

    /**
     * 判断time区间，获取单用户prop1
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkOkTime
     * @param isMaster
     * @return
     */
    List<BillCoinUserAssets> selectSingleByParamAndTimeSwapMain(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkOkTime, Boolean isMaster);

    /**
     * 获取最近一次快照记录
     *
     * @param accountType
     * @param accountParam
     * @param userId
     * @param checkOkTime
     * @param isMaster
     * @return
     */
    BillCoinUserAssets selectRecentNearSnapTime(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("checkTime") Date checkOkTime, Boolean isMaster);


    /**
     * 获取用户该对帐时间的所有币种对帐记录
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param checkOkTime
     * @param isMaster
     * @return
     */
    List<BillCoinUserAssets> selectRecordsByUserIdAndCheckTime(
            @Param("userId") Long userId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkOkTime, Boolean isMaster);

    List<BillCoinUserAssets> selectRecordsByUserIdAndTime(
            @Param("userId") Long userId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkOkTime, Boolean isMaster);

    List<BillCoinUserAssets> selectRecordsByUserIdsAndTime(
            @Param("userIds") List<Long> userIds,
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkOkTime,
            Boolean isMaster);

    /**
     * 获取用户下不同币种的时间快照
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param isMaster
     * @return
     */
    BillCoinUserAssets selectRecordLimitByUserCoin(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime,
            Boolean isMaster);

    /**
     * 获取用户下不同币种的时间快照
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param isMaster
     * @return
     */
    BillCoinUserAssets selectRecordLimitByUserCoinSwapMain(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime,
            Boolean isMaster);

    /**
     * 获取用户下币种的快照记录（对帐时间一直更新，初始化时间不变）
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param isMaster
     * @return
     */
    BillCoinUserAssets selectCheckTimeOverInitLimitByUerCoin(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime, Boolean isMaster);

    BillCoinUserAssets selectCheckTimeOverInitLimitByUerCoinSwapMain(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime, Boolean isMaster);

    /**
     * 获取该用户此时间点的一条记录
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param isMaster
     * @return
     */
    BillCoinUserAssets selectRecordsByUserLimit(
            @Param("userId") Long userId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam, Boolean isMaster);

    /**
     * 获取对帐时间记录的的所有币种
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param isMaster
     * @return
     */
    List<BillCoinUserAssets> selectCoinsByUserAndTime(
            @Param("userId") Long userId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime,
            Boolean isMaster);

    /**
     * 获取存在仓位的用户集合
     *
     * @param accountType 账户类型
     * @param accountParam 账户参数
     * @param minId 最小id
     * @param maxId 最大id
     * @return
     */
    List<Long> selectUsersWithPosition(Integer accountType, String accountParam, Long minId, Long maxId);

    /**
     * 获取存在仓位的用户集合
     *
     * @param accountType 账户类型
     * @param accountParam 账户参数
     * @param minId 最小id
     * @param initialTime 初始化时间
     * @return
     */
    List<Long> selectPositionUsersBetweenIdAndTime(@Param("accountType") Integer accountType,
                                                   @Param("accountParam") String accountParam,
                                                   @Param("minId") Long minId,
                                                   @Param("initialTime") Date initialTime);


    /**
     * 删除历史记录
     *
     * @param ids
     * @param accountType
     * @param accountParam
     * @return
     */
    int deleteByIds(@Param("ids") List<Long> ids,
                    @Param("accountType") String accountType,
                    @Param("accountParam") String accountParam);


    /**
     * 获取表中截止时间内最大的maxId
     *
     * @param accountType 账户类型
     * @param accountParam 账户参数
     * @return
     */
    Long selectMaxId(@Param("accountType") Integer accountType,
                     @Param("accountParam") String accountParam);

    /**
     * 根据用户id和时间查询对账记录
     *
     * @param userId 用户id
     * @param checkOkTime 对账成功时间
     * @param accountType 账户类型
     * @param accountParam 账户参数
     * @return
     */
    List<BillCoinUserAssets> selectByUserAndTime(@Param("userId") Long userId,
                                                 @Param("checkOkTime") Date checkOkTime,
                                                 @Param("accountType") String accountType,
                                                 @Param("accountParam") String accountParam);

    /**
     * 根据用户id和时间查询对账记录
     *
     * @param userId       用户id
     * @param checkOkTime  对账成功时间
     * @param accountType  账户类型
     * @param accountParam 账户参数
     * @return
     */
    List<BillCoinUserAssets> selectByUserAndCheckTime(@Param("userId") Long userId,
                                                      @Param("checkOkTime") Date checkOkTime,
                                                      @Param("accountType") String accountType,
                                                      @Param("accountParam") String accountParam);

    /**
     * 删除用户资产记录
     * @param ids 主键集合
     * @param accountType 账户类型
     * @param accountParam 账户参数
     * @return void
     * @throws
     * @Date 2022/7/11 11:59 AM
     * <AUTHOR>
     */
    int deleteUserCoinAssetsHistoryRecord(@Param("ids") List<Long> ids,
                                           @Param("accountType") String accountType,
                                           @Param("accountParam") String accountParam);


    /**
     * 查询用户资产记录
     * @param queryUserDTO 查询资产dto
     * @return void
     * @throws
     * @Date 2022/7/11 11:59 AM
     * <AUTHOR>
     */
    List<Long> selectUserCoinAssetsHistoryRecord(QueryUserDTO queryUserDTO);

    /**
     * 分页查询 查询有仓位的用户列表
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param pageSize
     * @return
     */
    List<BillCoinUserAssets> queryList(
                          @Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("maxId") Long maxId,
                          @Param("pageSize") Integer pageSize);

    /**
     * 批量插入仓位备份表数据
     * @param list
     * @param accountType
     * @param accountParam
     */
    int batchInsertBackup(@Param("list") List<BillCoinUserAssets> list,
                           @Param("accountType") Byte accountType,
                           @Param("accountParam") String accountParam);

    /**
     * 查询用户资产和仓位参数信息
     * @param checkTime
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param pageSize
     * @return
     */
    List<BillCoinUserAssets> queryByCheckTimeAndParam(@Param("checkTime") Date checkTime,
                                                      @Param("accountType") Byte accountType,
                                                      @Param("accountParam") String accountParam,
                                                      @Param("maxId") Long maxId,
                                                      @Param("pageSize") Integer pageSize);

    /**
     * 获取表中截止时间内最大的maxId
     *
     * @param accountType 账户类型
     * @param accountParam 账户参数
     * @return
     */
    Long selectSyncMaxId(@Param("accountType") Integer accountType,
                     @Param("accountParam") String accountParam);


    /**
     * 查询用户资产信息
     * @param checkTime
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param pageSize
     * @return
     */
    List<BillCoinUserAssets> queryByCheckTimeList(@Param("checkTime") Date checkTime,
                                                      @Param("accountType") Byte accountType,
                                                      @Param("accountParam") String accountParam,
                                                      @Param("maxId") Long maxId,
                                                      @Param("pageSize") Integer pageSize);

    /**
     * 获取最大的maxId和minId
     * @param checkTime
     * @param accountType
     * @param accountParam
     * @return {@link Long }
     * <AUTHOR>
     * @date 2023/3/2 11:21
     */
    PageIdResponse selectByCheckTimeMaxMinId(@Param("checkTime") Date checkTime,
                                             @Param("accountType") Byte accountType,
                                             @Param("accountParam") String accountParam);

    /**
     * 查询用户资产信息
     * @param checkTime
     * @param accountType
     * @param accountParam
     * @param minId
     * @param maxId
     * @return
     */
    List<BillCoinUserAssets> queryByCheckTimeSliceList(@Param("checkTime") Date checkTime,
                                                  @Param("accountType") Byte accountType,
                                                  @Param("accountParam") String accountParam,
                                                  @Param("minId") Long minId,
                                                  @Param("maxId") Long maxId);

    /**
     * 查询用户资产
     *
     * @param accountType
     * @param accountParam
     * @param userId
     * @param coinId
     * @param isMaster
     * @return
     */
    BillCoinUserAssets selectUserAssetsByUserIdAndCoinId(@Param("accountType") Byte accountType,
                                                         @Param("accountParam") String accountParam,
                                                         @Param("userId") Long userId,
                                                         @Param("coinId") Integer coinId,
                                                         boolean isMaster);


    /**
     * 根据用户id查询用户最新资产
     *
     * @param userId 用户id
     * @param accountType 账户类型
     * @param accountParam 账户参数
     * @return
     */
    List<BillCoinUserAssets> selectLastUserAssets(@Param("userId") Long userId,
                                                  @Param("accountType") Byte accountType,
                                                  @Param("accountParam") String accountParam);
}