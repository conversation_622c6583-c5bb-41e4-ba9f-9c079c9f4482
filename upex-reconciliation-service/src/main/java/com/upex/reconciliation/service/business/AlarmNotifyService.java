package com.upex.reconciliation.service.business;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalListener;
import com.github.benmanes.caffeine.cache.Scheduler;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateLevelEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateQpsEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateTypeEnum;
import com.upex.reconciliation.service.dao.entity.AlarmNotifyTemplate;
import com.upex.reconciliation.service.dao.entity.BillAlarmLog;
import com.upex.reconciliation.service.dao.mapper.AlarmNotifyTemplateMapper;
import com.upex.reconciliation.service.model.alarm.*;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.model.enums.AlarmStatusEnum;
import com.upex.reconciliation.service.service.BillAlarmLogService;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.utils.EnvUtil;
import com.upex.reconciliation.service.utils.FreeMarkerUtils;
import com.upex.reconciliation.service.utils.LarkNotifyUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.log.AlarmUtils;
import com.upex.utils.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.service.client.cex.CexConstants.CEX_ALARM_pre;

@Slf4j
@Service
public class AlarmNotifyService implements Runnable {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "alarmNotifyTemplateMapper")
    private AlarmNotifyTemplateMapper alarmNotifyTemplateMapper;
    /***消息通知队列***/
    private BlockingQueue<AlarmNotifyModel> alarmNotifyQueue = new ArrayBlockingQueue<>(100);
    /***消息模板幂等缓存***/
    private Map<String, Cache<String, Boolean>> idempotentCacheMap = new ConcurrentHashMap<>();

    /**
     * 告警缓存
     */
    private Cache<String, AlarmNotifyModel> alarmCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .scheduler(Scheduler.forScheduledExecutorService(Executors.newScheduledThreadPool(1)))
            .removalListener((RemovalListener<String, AlarmNotifyModel>) (key, value, removalCause) -> addAlarmLog(value))
            .build();

    @Resource
    private BillAlarmLogService billAlarmLogService;

    /***构造方法***/
    public AlarmNotifyService() {
        for (AlarmTemplateQpsEnum qpsEnum : AlarmTemplateQpsEnum.values()) {
            if (qpsEnum.getCode().equals(AlarmTemplateQpsEnum.NONE.getCode())) {
                continue;
            }
            Cache<String, Boolean> cache = Caffeine.newBuilder().expireAfterWrite(qpsEnum.getTime(), TimeUnit.MILLISECONDS).build();
            idempotentCacheMap.put(qpsEnum.getCode(), cache);
        }
        new Thread(this, "alarm-notify-thread").start();
    }

    public void alarmException(String message) {
        message = StringUtils.defaultString(message);
        alarm(AlarmTemplateEnum.RECON_EXCEPTION_ALARM_TEMPLATE, message);
    }

    public void alarm(AlarmTemplateEnum alarmTemplateEnum) {
        alarm((Byte) null, alarmTemplateEnum, (Object[]) null);
    }

    public void alarm(AlarmTemplateEnum alarmTemplateEnum, Map<String, Object> arg) {
        alarm((Byte) null, alarmTemplateEnum, arg);
    }

    public void alarm(AlarmTemplateEnum alarmTemplateEnum, Object... arg) {
        alarm((Byte) null, alarmTemplateEnum, arg);
    }

    public void alarm(String accountType, AlarmTemplateEnum alarmTemplateEnum, Map<String, Object> arg) {
        alarm(AlarmParam.builder().templateEnumName(alarmTemplateEnum != null ? alarmTemplateEnum.name() : null).accountType(accountType != null ? accountType.toString() : null).arg(arg).build());
    }

    public void alarm(String accountType, AlarmTemplateEnum alarmTemplateEnum, Object... arg) {
        alarm(AlarmParam.builder().templateEnumName(alarmTemplateEnum != null ? alarmTemplateEnum.name() : null).accountType(accountType != null ? accountType.toString() : null).arg(arg).build());
    }

    public void alarm(Byte accountType, AlarmTemplateEnum alarmTemplateEnum, Map<String, Object> arg) {
        alarm(AlarmParam.builder().templateEnumName(alarmTemplateEnum != null ? alarmTemplateEnum.name() : null).accountType(accountType != null ? accountType.toString() : null).arg(arg).build());
    }

    public void alarm(Byte accountType, AlarmTemplateEnum alarmTemplateEnum, Object... arg) {
        alarm(AlarmParam.builder().templateEnumName(alarmTemplateEnum != null ? alarmTemplateEnum.name() : null).accountType(accountType != null ? accountType.toString() : null).arg(arg).build());
    }

    public void cexAlarm(AlarmTemplateEnum alarmTemplateEnum, Object... arg) {
        if(!alarmTemplateEnum.name().startsWith(CEX_ALARM_pre)){
            return;
        }
        //当且仅当配置了告警沉默时间时，且告警静默时间大于当前时间才不告警
        Map<String, ApolloThirdCexAssetConfig.ApiAlarmConfig> cexAlarmConfigs=ReconciliationApolloConfigUtils.getThirdCexAssetConfig().getCexAlarmConfigs();
        if(cexAlarmConfigs==null){
            alarm(alarmTemplateEnum, arg);
        }else{
            ApolloThirdCexAssetConfig.ApiAlarmConfig apiAlarmConfig=cexAlarmConfigs.get(alarmTemplateEnum.name());
            if(apiAlarmConfig!=null){
                if(apiAlarmConfig.getIfAlarm()){
                    alarm(alarmTemplateEnum, arg);
                }else{
                    if(System.currentTimeMillis()>apiAlarmConfig.getAlarmSilenceTime()){
                        alarm(alarmTemplateEnum, arg);
                    }
                }
            }else{
                alarm(alarmTemplateEnum, arg);
            }
        }
    }

    public void cexApipathAlarm(AlarmTemplateEnum alarmTemplateEnum,Object... arg){
        if(!alarmTemplateEnum.name().startsWith(CEX_ALARM_pre)){
            return;
        }
        //当且仅当配置了告警沉默时间时，且命中userId、apiPath规则且告警静默时间大于当前时间才不告警
        ApolloThirdCexAssetConfig apolloThirdCexAssetConfig = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
        if (apolloThirdCexAssetConfig.getApiAlarmConfigs() != null) {
            if (apolloThirdCexAssetConfig.getApiAlarmConfigs().containsKey(arg[0])) {
                List<ApolloThirdCexAssetConfig.ApiAlarmConfig> apiAlarmConfigs = apolloThirdCexAssetConfig.getApiAlarmConfigs().get(arg[0]);
                Optional<ApolloThirdCexAssetConfig.ApiAlarmConfig> apiAlarmConfig = apiAlarmConfigs.stream().filter(filterApiAlarmConfig -> filterApiAlarmConfig.getApiPath().equals(arg[2])).findFirst();
                if (apiAlarmConfig.isPresent()) {
                    if (apiAlarmConfig.get().getIfAlarm()) {
                        //明确告警
                        alarm(alarmTemplateEnum, arg);
                    } else {
                        if (System.currentTimeMillis()>apiAlarmConfig.get().getAlarmSilenceTime()) {
                            //到了告警静默时间也告警，否则在告警静默时间内不告警
                            alarm(alarmTemplateEnum, arg);
                        }
                    }
                }else{
                    alarm(alarmTemplateEnum, arg);
                }
            }else{
                alarm(alarmTemplateEnum, arg);
            }
        } else {
            alarm(alarmTemplateEnum, arg);
        }
    }

    public void alarm(String name, Map<String, Object> arg) {
        AlarmNotifyModel alarmNotifyModel = new AlarmNotifyModel();
        alarmNotifyModel.setAlarmTemplateModel(AlarmTemplateEnum.getAlarmTemplateModel(name));
        alarmNotifyModel.setArg(arg);
        pushAlarmNotifyQueue(alarmNotifyModel);
    }

    public void alarm(String name, Object... arg) {
        AlarmNotifyModel alarmNotifyModel = new AlarmNotifyModel();
        alarmNotifyModel.setAlarmTemplateModel(AlarmTemplateEnum.getAlarmTemplateModel(name));
        alarmNotifyModel.setArg(arg);
        pushAlarmNotifyQueue(alarmNotifyModel);
    }

    public void alarm(AlarmParam alarmParam) {
        AlarmNotifyModel alarmNotifyModel = new AlarmNotifyModel();
        alarmNotifyModel.setAccountType(alarmParam.getAccountType());
        alarmNotifyModel.setAlarmTemplateModel(AlarmTemplateEnum.getAlarmTemplateModel(alarmParam.getTemplateEnumName()));
        alarmNotifyModel.setArg(alarmParam.getArg());
        alarmNotifyModel.setContent(alarmParam.getContent());
        alarmNotifyModel.setAtUserList(alarmParam.getAtUserList());
        pushAlarmNotifyQueue(alarmNotifyModel);
    }

    /**
     * 放入消息队列
     *
     * @return
     */
    private boolean pushAlarmNotifyQueue(AlarmNotifyModel alarmNotifyModel) {
        // 幂等判断
        AlarmTemplateModel alarmTemplateModel = alarmNotifyModel.getAlarmTemplateModel();
        if (alarmTemplateModel != null) {
            AlarmTemplateQpsEnum qpsEnum = alarmTemplateModel.getQpsEnum();
            if (!qpsEnum.getCode().equals(AlarmTemplateQpsEnum.NONE.getCode())) {
                Cache<String, Boolean> cacheMap = idempotentCacheMap.get(qpsEnum.getCode());
                if (cacheMap != null) {
                    Boolean value = cacheMap.getIfPresent(alarmTemplateModel.getName());
                    if (value != null) {
                        log.info("AlarmNotifyService.pushAlarmNotifyQueue rate limit data:{}", JSON.toJSONString(alarmNotifyModel));
                        return false;
                    } else {
                        cacheMap.put(alarmTemplateModel.getName(), true);
                    }
                }
            }
        }
        // 放入队列
        boolean result = alarmNotifyQueue.offer(alarmNotifyModel);
        if (!result) {
            log.error("AlarmNotifyService.pushAlarmNotifyQueue error data:{}", JSON.toJSONString(alarmNotifyModel));
        }
        return result;
    }

    @Override
    public void run() {
        while (true) {
            try {
                AlarmNotifyModel alarmNotifyModel = alarmNotifyQueue.take();
                if (alarmNotifyModel != null) {
                    AlarmNotifyConfig alarmNotifyConfig = ReconciliationApolloConfigUtils.getAlarmNotifyConfig();
                    String accountType = alarmNotifyModel.getAccountType();
                    if ((StringUtils.isNotEmpty(accountType) && alarmNotifyConfig.getSendLarkOffAccountType().contains(accountType))) {
                        continue;
                    }
                    if (!alarmNotifyConfig.isSendLarkOpen() || StringUtils.isBlank(alarmNotifyConfig.getSendLarkApi())) {
                        continue;
                    }
                    String alarmTemplateCode = alarmNotifyModel.getAlarmTemplateModel() != null ? alarmNotifyModel.getAlarmTemplateModel().getCode() : "";
                    String configKey = alarmTemplateCode + (accountType != null ? "-" + accountType : "");
                    if (alarmNotifyConfig.getSendLarkOffTemplateCode().contains(configKey) || alarmNotifyConfig.getSendLarkOffTemplateCode().contains(alarmTemplateCode)) {
                        continue;
                    }
                    // 组装参数
                    alarmNotifyModel.setSendLarkRetryCount(alarmNotifyConfig.getSendLarkRetryCount());
                    alarmNotifyModel.setSendLarkRetryInterval(alarmNotifyConfig.getSendLarkRetryInterval());
                    alarmNotifyModel.setSendLarkSecret(alarmNotifyConfig.getSendLarkSecret());
                    // 组装@
                    List<String> atUserList = alarmNotifyModel.getAtUserList();
                    List<String> atUserList1 = alarmNotifyConfig.getAtUserNotifyMap().get(configKey);
                    List<String> atUserList2 = alarmNotifyConfig.getAtUserNotifyMap().get(alarmTemplateCode);
                    List<String> atUserList3 = alarmNotifyConfig.getAtUserNotifyMap().get(Optional.ofNullable(accountType).orElse(""));
                    List<String> defaultAtUserList = alarmNotifyConfig.getDefaultAtUserList();
                    alarmNotifyModel.setAtUserList(CollectionUtils.isNotEmpty(atUserList) ? atUserList :
                            CollectionUtils.isNotEmpty(atUserList1) ? atUserList1 :
                                    CollectionUtils.isNotEmpty(atUserList2) ? atUserList2 :
                                            CollectionUtils.isNotEmpty(atUserList3) ? atUserList3 : defaultAtUserList);
                    // 组装发送api
                    String sendLarkApi = alarmNotifyModel.getSendLarkApi();
                    String sendLarkApi1 = alarmNotifyConfig.getPreNotifyConfig().get(configKey);
                    String sendLarkApi2 = alarmNotifyConfig.getPreNotifyConfig().get(alarmTemplateCode);
                    String sendLarkApi3 = alarmNotifyConfig.getPreNotifyConfig().get(Optional.ofNullable(accountType).orElse(""));
                    String defaultSendLarkApi = alarmNotifyConfig.getSendLarkApi();
                    alarmNotifyModel.setSendLarkApi(StringUtils.isNotEmpty(sendLarkApi) ? sendLarkApi :
                            StringUtils.isNotEmpty(sendLarkApi1) ? sendLarkApi1 :
                                    StringUtils.isNotEmpty(sendLarkApi2) ? sendLarkApi2 :
                                            StringUtils.isNotEmpty(sendLarkApi3) ? sendLarkApi3 : defaultSendLarkApi);
                    // 组装 content
                    if (alarmNotifyModel.getAlarmTemplateModel() == null) {
                        alarmNotifyModel.setContent(MessageFormatter.arrayFormat(alarmNotifyModel.getContent(), (Object[]) alarmNotifyModel.getArg()).getMessage());
                    } else if (alarmNotifyModel.getAlarmTemplateModel().getType() == AlarmTemplateTypeEnum.LOG) {
                        StringBuilder contentSb = new StringBuilder();
                        if (alarmNotifyModel.getArg() instanceof Map) {
                            Map<String, Object> args = new HashMap<>((Map<String, Object>) alarmNotifyModel.getArg());
                            List<String> atUserIdList = ListUtils.emptyIfNull(alarmNotifyModel.getAtUserList()).stream().map(LarkNotifyUtil::getAtUserId).filter(Objects::nonNull).collect(Collectors.toList());
                            args.put("atUserIdList", atUserIdList);
                            String content = FreeMarkerUtils.processToString(alarmNotifyModel.getAlarmTemplateModel().getName(), alarmNotifyModel.getAlarmTemplateModel().getText(), args);
                            contentSb.append(content);
                        } else {
                            Object[] args = (Object[]) alarmNotifyModel.getArg();
                            contentSb.append(MessageFormatter.arrayFormat(alarmNotifyModel.getAlarmTemplateModel().getText(), args).getMessage());
                            if (CollectionUtils.isNotEmpty(alarmNotifyModel.getAtUserList())) {
                                alarmNotifyModel.getAtUserList().forEach(item -> {
                                    String[] userId = item.split("\\|");
                                    if (userId.length > 1) {
                                        contentSb.append("<at user_id=\"" + userId[1] + "\">" + userId[0] + "</at>");
                                    }
                                });
                            }
                        }
                        alarmNotifyModel.setContent(contentSb.toString());
                    } else if (alarmNotifyModel.getAlarmTemplateModel().getType() == AlarmTemplateTypeEnum.FREEMARKER) {
                        AlarmNotifyTemplate alarmNotifyTemplate = queryTemplateByCode(alarmNotifyModel.getAlarmTemplateModel().getCode());
                        if (alarmNotifyTemplate == null) {
                            alarmNotifyModel.setContent("模板为空，请检查配置 " + alarmNotifyModel.getAlarmTemplateModel().getCode());
                        } else {
                            List<String> atUserIdList = ListUtils.emptyIfNull(alarmNotifyModel.getAtUserList()).stream().map(LarkNotifyUtil::getAtUserId).filter(Objects::nonNull).collect(Collectors.toList());
                            Map<String, Object> args = new HashMap<>((Map<String, Object>) alarmNotifyModel.getArg());
                            args.put("atUserIdList", atUserIdList);
                            String content = FreeMarkerUtils.processToString(alarmNotifyTemplate.getTemplateCode(), alarmNotifyTemplate.getTemplateContent(), args);
                            alarmNotifyModel.setContent(content.toString());
                        }
                    }

                    // 缓存
                    putCache(alarmNotifyModel);

                    LarkNotifyUtil.sendLarkMessage(alarmNotifyModel);
                    // 每天23-06点24小时电话报警
                    if (Objects.equals(alarmNotifyModel.getAlarmTemplateModel().getLevel().getCode(), AlarmTemplateLevelEnum.EMERGENCY.getCode()) && alarmNotifyConfig.isTwentyFourHourAlarmOpen()) {
                        AlarmUtils.emergency("您有lark告警，请注意查看");
                    }
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 缓存
     *
     * @param alarmNotifyModel
     */
    private void putCache(AlarmNotifyModel alarmNotifyModel) {
        // 监控告警存盘黑名单
        AlarmNotifyConfig alarmNotifyConfig = ReconciliationApolloConfigUtils.getAlarmNotifyConfig();
        List<String> alarmSaveBlackList = alarmNotifyConfig.getAlarmSaveBlackList();
        if (CollectionUtils.isNotEmpty(alarmSaveBlackList) && alarmSaveBlackList.contains(alarmNotifyModel.getAlarmTemplateModel().getCode())) {
            return;
        }
        String contentMd5 = MD5Util.getMD5String(alarmNotifyModel.getContent());
        AlarmNotifyModel present = alarmCache.getIfPresent(contentMd5);
        // 存在则返回
        if (Objects.nonNull(present)) {
            return;
        }
        alarmNotifyModel.setAlarmTime(new Date());
        // 告警缓存
        alarmCache.put(contentMd5, alarmNotifyModel);
    }

    /**
     * 入库
     *
     * @param alarmNotifyModel
     */
    private void addAlarmLog(AlarmNotifyModel alarmNotifyModel) {
        try {
            BillAlarmLog alarmLog = new BillAlarmLog();
            alarmLog.setCode(alarmNotifyModel.getAlarmTemplateModel().getCode());
            alarmLog.setAlarmTime(alarmNotifyModel.getAlarmTime());
            if (Objects.nonNull(alarmNotifyModel.getAlarmTemplateModel().getQpsEnum())) {
                alarmLog.setFrequency(alarmNotifyModel.getAlarmTemplateModel().getQpsEnum().getCode());
            } else {
                alarmLog.setFrequency("");
            }
            alarmLog.setSource(EnvUtil.getServiceInstanceName());
            alarmLog.setAccountType(StringUtils.isBlank(alarmNotifyModel.getAccountType()) ? "" :  alarmNotifyModel.getAccountType());
            alarmLog.setContent(alarmNotifyModel.getContent());
            alarmLog.setReason("");
            alarmLog.setAtUserList("");
            if (CollectionUtils.isNotEmpty(alarmNotifyModel.getAtUserList())) {
                List<String> atUserList = alarmNotifyModel.getAtUserList().stream().map(atUser -> {
                            String[] userId = atUser.split("\\|");
                            return userId.length > 1 ? userId[0] : null;
                        })
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
                alarmLog.setAtUserList(JSON.toJSONString(atUserList));
            }
            alarmLog.setStatus(AlarmStatusEnum.UN_PROCESSED.getCode());
            alarmLog.setCreateTime(new Date());
            alarmLog.setUpdateTime(new Date());
            billAlarmLogService.insert(alarmLog);
        } catch (Exception e) {
            log.error("addAlarmLog alarmNotifyModel:{} error:", JSON.toJSONString(alarmNotifyModel), e);
        }
    }

    /**
     * 查询数据库模板
     *
     * @param templateCode
     * @return
     */
    private AlarmNotifyTemplate queryTemplateByCode(String templateCode) {
        return dbHelper.doDbOpInReconMaster(() -> alarmNotifyTemplateMapper.queryTemplateByCode(templateCode));
    }


    public int batchInsert(List<AlarmNotifyTemplate> records) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> alarmNotifyTemplateMapper.batchInsert(records));
        }
        return 0;
    }


    public int updateAlarmTemplate(String templateCode, String templateContent, String templateName) {
        Date now = new Date();
        return dbHelper.doDbOpInReconMaster(() -> alarmNotifyTemplateMapper.updateTemplateContentByCode(templateCode, templateContent, templateName, now));

    }


    public void addOrUpdateAlarmTemplate() {
        AlarmTemplateApolloConfig alarmTemplateApolloConfig = ReconciliationApolloConfigUtils.getAlarmTemplateApolloConfig();

        if (StringUtils.isNotBlank(alarmTemplateApolloConfig.getAction()) && alarmTemplateApolloConfig.getAction().equalsIgnoreCase("add")) {
            Date now = new Date();
            AlarmNotifyTemplate alarmNotifyTemplate = new AlarmNotifyTemplate();
            alarmNotifyTemplate.setTemplateCode(alarmTemplateApolloConfig.getTemplateCode());
            alarmNotifyTemplate.setTemplateName(alarmTemplateApolloConfig.getTemplateName());
            alarmNotifyTemplate.setTemplateContent(alarmTemplateApolloConfig.getTemplateContent());
            alarmNotifyTemplate.setVersion(alarmTemplateApolloConfig.getVersion() != null ? alarmTemplateApolloConfig.getVersion() : 1);
            alarmNotifyTemplate.setCreateTime(now);
            alarmNotifyTemplate.setUpdateTime(now);
            List<AlarmNotifyTemplate> list = new ArrayList<>();
            list.add(alarmNotifyTemplate);
            batchInsert(list);
        } else {
            updateAlarmTemplate(alarmTemplateApolloConfig.getTemplateCode(), alarmTemplateApolloConfig.getTemplateContent(), alarmTemplateApolloConfig.getTemplateName());
        }
    }

    public  static void main(String []args){
        AlarmTemplateEnum alarmTemplateEnum = AlarmTemplateEnum.THIRD_CEX_API_EXCEPTION;
        String json="{\n" +
                "    \"cexAssetSyncConfigHashMap\": {\n" +
                "        \"pay_transfer\": {\n" +
                "            \"maxQueryDay\": 7,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000\n" +
                "        },\n" +
                "        \"parent_sub_transfer\": {\n" +
                "            \"maxQueryDay\": 7,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1750918742000,\n" +
                "            \"initRefersh\": true,\n" +
                "            \"initSetTime\": 1750919414000\n" +
                "        },\n" +
                "        \"sub_balance\": {\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000\n" +
                "        },\n" +
                "        \"parent_balance\": {\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000\n" +
                "        },\n" +
                "        \"deposite\": {\n" +
                "            \"maxQueryDay\": 30,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000,\n" +
                "            \"initRefersh\": true,\n" +
                "            \"initSetTime\": 1750917608000\n" +
                "        },\n" +
                "        \"parent_universial_transfer\": {\n" +
                "            \"maxQueryDay\": 7,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000,\n" +
                "            \"initRefersh\": true,\n" +
                "            \"initSetTime\": 1738339200000\n" +
                "        },\n" +
                "        \"withdraw\": {\n" +
                "            \"maxQueryDay\": 90,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000,\n" +
                "            \"initRefersh\": true,\n" +
                "            \"initSetTime\": 1750768585000\n" +
                "        }\n" +
                "    },\n" +
                "    \"ifParallelQueryAsset\": false,\n" +
                "    \"querySecretSave\": 30,\n" +
                "    \"sqlInsertSize\": 300,\n" +
                "    \"syncRecordInitTime\": 1672502400000,\n" +
                "    \"timeInverval\": 5,\n" +
                "    \"tmpSecretSave\": 120,\n" +
                "    \"cexAlarmConfigs\": {\n" +
                "        \"THIRD_CEX_API_EXCEPTION\": {\n" +
                "            \"ifAlarm\": false,\n" +
                "            \"alarmSilenceTime\": \"1750997652000\"\n" +
                "        }\n" +
                "    }\n" +
                "}";
        Map<String, ApolloThirdCexAssetConfig.ApiAlarmConfig> cexAlarmConfigs = ((ApolloThirdCexAssetConfig)JSON.parseObject(json, ApolloThirdCexAssetConfig.class)).getCexAlarmConfigs();

        if(cexAlarmConfigs==null){
            System.out.println("告警");
        }else{
            ApolloThirdCexAssetConfig.ApiAlarmConfig apiAlarmConfig=cexAlarmConfigs.get(alarmTemplateEnum.name());
            if(apiAlarmConfig!=null){
                if(apiAlarmConfig.getIfAlarm()){
                    System.out.println("告警");
                }else{
                    if(System.currentTimeMillis()>apiAlarmConfig.getAlarmSilenceTime()){
                        System.out.println("告警");
                    }
                }
            }else{
                System.out.println("告警");
            }
        }
    }
}
