package com.upex.reconciliation.service.business.convert.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class InnerTransferOrder extends MainOrder {
    private Long id;
//    private Long passiveOrderId;
//    private Long passiveAccountId;
//    private Integer coinId;
//    private BigDecimal amount;
//    private Integer transferType;
//    private Integer status;
//    private String params;
//    private Date creaInnerTransferOrderteTime;
//    private Date updateTime;
//    private Long version;
}
