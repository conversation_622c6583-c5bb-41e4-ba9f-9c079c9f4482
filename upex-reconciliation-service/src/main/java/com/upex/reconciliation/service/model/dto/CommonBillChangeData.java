package com.upex.reconciliation.service.model.dto;

import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.mixcontract.common.literal.enums.HoldModeEnum;
import com.upex.mixcontract.common.literal.enums.HoldSideEnum;
import com.upex.mixcontract.common.literal.enums.MarginModeEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.common.constants.BillBizTypeConstant;
import com.upex.reconciliation.service.common.constants.enums.CommonBillChangeDataMsgType;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.utils.BeanCopierUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonBillChangeData extends AbstractProperty implements IMsg {
    private byte accountType;
    private Long accountId;
    private String bizType;
    private Integer coinId;
    private String symbolId;
    private Long offset;
    private Integer partition;
    private Long messageId;
    private Long bizId;
    private Date createTime;
    private String params;
    /***过期数据，直接丢弃***/
    private Integer status;
    /***业务时间，用于后续的错误队列里的排序等***/
    private Date bizTime;
    /***从业务主键id中解析出来的时间***/
    private Date bizTimeFromId;
    /***异步初始化之后得到的资产数据，如果非空的，需要处理，大部分情况下为空***/
    //private List<AccountAssetsInfoResult> accountAssets;
    /***异步查询资产信息***/
    private List<BillCoinUserProperty> billCoinUserProperties;
    private boolean initialized = false;
    private MarginModeEnum marginMode;
    private HoldModeEnum holdMode;
    private HoldSideEnum holdSide;
    private BigDecimal countChange;
    private BillCoinUserProperty billCoinUserProperty;
    /***消息类型***/
    private CommonBillChangeDataMsgType msgType = CommonBillChangeDataMsgType.NORMAL;
    private Long kafkaTimestamp;
    private Long consumerTimestamp;
    /***合约tokenId***/
    private String tokenId;
    /***忽略用户资产对账 仅限于时间片change_prop累加 比如：理财 派息***/
    private boolean ignoreUserPropCheck = false;
    private Date lastCheckOkTime;
    private String orderId;
    private String secondBusinessLine;
    private BigDecimal changeTransferFee = BigDecimal.ZERO;

    public CommonBillChangeData(MessageMonitor messageMonitor) {

    }

    public static CommonBillChangeData getInstance(AccountAssetsInfoResult assetsInfoResult) {
        return new CommonBillChangeData();
    }

    @Override
    public Long getMsgCreateTime() {
        return createTime != null ? createTime.getTime() : 0L;
    }

    @Override
    public Long getBizId() {
        return bizId;
    }


    public Date getUserCheckBizTime() {
        if (bizTimeFromId != null) {
            return bizTimeFromId;
        }
        return bizTime;
    }

    /**
     * errormap排序聚合key
     *
     * @return
     */
    public String errorMapGroupByKey() {
        return AccountTypeEnum.toEnum(accountType).isUserCoinPropSubType() ? coinId + "#" + symbolId : coinId.toString();
    }

    public String groupByUserSymbolCoin() {
        return this.accountId + "#" + this.symbolId + "#" + this.coinId;
    }

    /**
     * 获取混合合约换汇数据
     *
     * @return
     */
    public CommonBillChangeData buildMixContractExchangeData(BillCheckService billCheckService, Long exchangeUserId) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        CommonBillChangeData exchangeData = new CommonBillChangeData();
        BeanCopierUtil.copyProperties(this, exchangeData);
        exchangeData.setChangeProp1(BigDecimal.ZERO);
        exchangeData.setChangeProp2(BigDecimal.ZERO);
        exchangeData.setChangeProp3(BigDecimal.ZERO);
        exchangeData.setChangeProp4(BigDecimal.ZERO);
        exchangeData.setChangeProp5(BigDecimal.ZERO);
        exchangeData.setChangeProp6(BigDecimal.ZERO);
        exchangeData.setChangeProp7(BigDecimal.ZERO);
        exchangeData.setChangeProp8(BigDecimal.ZERO);
        exchangeData.setProp1(BigDecimal.ZERO);
        exchangeData.setProp2(BigDecimal.ZERO);
        exchangeData.setProp3(BigDecimal.ZERO);
        exchangeData.setProp4(BigDecimal.ZERO);
        exchangeData.setProp5(BigDecimal.ZERO);
        exchangeData.setProp6(BigDecimal.ZERO);
        exchangeData.setProp7(BigDecimal.ZERO);
        exchangeData.setProp8(BigDecimal.ZERO);
        exchangeData.setBizType(BillBizTypeConstant.BILL_INNER_TRANS_MIDDLE_EXCHANGE);
        exchangeData.setAccountId(exchangeUserId);
        exchangeData.setChangeFee(BigDecimal.ZERO);
        exchangeData.setCountChange(BigDecimal.ZERO);
        exchangeData.setChangeProp2(billCheckService.getExchangeDataChangeAssets(this).negate());
        exchangeData.setProp2(billCheckService.getExchangeDataChangeAssets(this).negate());
        return exchangeData;
    }

    @Override
    public CommonBillChangeData clone() {
        try {
            ByteArrayOutputStream bo = new ByteArrayOutputStream();
            ObjectOutputStream oo = new ObjectOutputStream(bo);
            oo.writeObject(this);
            ByteArrayInputStream bi = new ByteArrayInputStream(bo.toByteArray());
            ObjectInputStream oi = new ObjectInputStream(bi);
            return (CommonBillChangeData) oi.readObject();
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }
}
