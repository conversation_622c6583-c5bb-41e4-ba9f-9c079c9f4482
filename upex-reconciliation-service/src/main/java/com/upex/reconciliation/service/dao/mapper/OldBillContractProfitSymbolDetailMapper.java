package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillContractProfitSymbolDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_symbol_detail(币种+币对维度盈亏明细表)】的数据库操作Mapper
 * @createDate 2023-06-09 17:19:14
 * @Entity com.upex.bill.domain.BillContractProfitSymbolDetail
 */
@Repository("oldBillContractProfitSymbolDetailMapper")
public interface OldBillContractProfitSymbolDetailMapper {

    List<BillContractProfitSymbolDetail> selectListByAccountTypeAndCheckTime(@Param("accountType") Byte accountType,
                                                                             @Param("accountParam") String accountParam,
                                                                             @Param("checkTime") Date checkTime);
}
