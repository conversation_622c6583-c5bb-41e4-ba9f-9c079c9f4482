package com.upex.reconciliation.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.bill.dto.enums.AssetsCheckTypeEnum;
import com.upex.bill.dto.params.AssetsBaseRequest;
import com.upex.bill.dto.results.AssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.impl.BillLedgerCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillTimeSliceCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.ProfitTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.PropertySubTypeEnum;
import com.upex.reconciliation.service.common.context.MonitorSceneContext;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.AssetsKafkaConfig;
import com.upex.reconciliation.service.model.config.AssetsKafkaConsumerConfig;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.SymbolCheckProperty;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.utils.*;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_SLICE_BASE_BILL_START;

/**
 * <AUTHOR>
 * @Date 2023/10/24
 */
@Service
@Slf4j
public class TimeSliceBillCheckService {
    @Resource
    private BillEngineManager billEngineManager;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Autowired
    private AssetsService internalAssetsService;
    @Resource
    private AssetsBillConfigService assetsBillConfigService;
    @Resource
    private AssetsBillCoinPropertyService assetsBillCoinPropertyService;
    @Resource
    private AssetsBillCoinTypePropertyService assetsBillCoinTypePropertyService;
    @Resource
    private ReconSystemAccountService reconSystemAccountService;
    @Autowired
    private AssetsCheckBiz assetsCheckBiz;
    @Autowired
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private CommonService commonService;
    @Resource
    private SerialNoGenerator noGenerator;
    @Resource
    private MonitorSceneContext monitorSceneContext;
    @Resource(name = "kafkaProducer")
    private KafkaProducer<String, String> kafkaProducer;
    @Resource
    private AssetsBillConfigSnapshotService assetsBillConfigSnapshotService;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    private Map<AssetsCheckTypeEnum, AssetsService> assetsCheckHandleMap = new ConcurrentHashMap<>();
    private Map<Byte, AtomicLong> ledgerKafkaPartitionCounterMap = new ConcurrentHashMap<>();

    @PostConstruct
    private void init() {
        assetsCheckHandleMap.put(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS, internalAssetsService);
        assetsCheckHandleMap.put(AssetsCheckTypeEnum.LEVER_SPOT_TOTAL_ASSETS, internalAssetsService);
    }

    public void baseBillStartByAccountType(Byte accountType) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        doBaseBillStart(apolloBizConfig);
    }

    public void ledgerCheckStartByAssetsType(String assetsType, String assetsParam) {
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(assetsType, assetsParam);
        if (assetsBillConfig == null) {
            return;
        }
        BillLedgerCheckModule billLedgerCheckModule = billEngineManager.getBillLedgerCheckModule(assetsType);
        if (billLedgerCheckModule == null) {
            return;
        }
        billLedgerCheckModule.doLedgerCheckStart();
    }

    public void ledgerSaveStartByAssetsType(String assetsType, String assetsParam) {
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(assetsType, assetsParam);
        if (assetsBillConfig == null) {
            return;
        }
        BillLedgerCheckModule billLedgerCheckModule = billEngineManager.getBillLedgerCheckModule(assetsType);
        if (billLedgerCheckModule == null) {
            return;
        }
        billLedgerCheckModule.doLedgerSaveDataStart();
    }


    public void inAllInit(Long checkTime, String accountType, String accountParam) {
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(accountType, accountParam);
        if (assetsBillConfig == null || checkTime == null) {
            return;
        }

        Date now = new Date();
        AssetsBaseRequest assetsBaseRequest = new AssetsBaseRequest();
        assetsBaseRequest.setAssetsCheckType(accountType);
        assetsBaseRequest.setAssetsCheckParam(accountParam);
        assetsBaseRequest.setEndTime(checkTime);
        // asset coin 表初始化
        List<AssetsInfoResult> assetsInfoResults = internalAssetsService.queryAllAssets(assetsBaseRequest);
        List<AssetsBillCoinProperty> assetsBillCoinProperties = assetsInfoResults.stream().map(item -> {
            AssetsBillCoinProperty assetsBillCoinProperty = BeanCopierUtil.copyProperties(item, AssetsBillCoinProperty.class);
            assetsBillCoinProperty.setCheckTime(new Date(checkTime));
            assetsBillCoinProperty.setCreateTime(now);
            assetsBillCoinProperty.setUpdateTime(now);
            return assetsBillCoinProperty;
        }).collect(Collectors.toList());
        assetsBillCoinPropertyService.batchInsert(assetsBillCoinProperties, accountType, accountParam);

        // asset coin_type 表初始化
        List<AssetsInfoResult> assetsInfoCoinTypeResults = internalAssetsService.queryAllAssetsType(assetsBaseRequest);
        List<AssetsBillCoinTypeProperty> assetsBillCoinTypeProperties = assetsInfoCoinTypeResults.stream().map(item -> {
            AssetsBillCoinTypeProperty assetsBillCoinTypeProperty = BeanCopierUtil.copyProperties(item, AssetsBillCoinTypeProperty.class);
            assetsBillCoinTypeProperty.setCheckTime(new Date(checkTime));
            assetsBillCoinTypeProperty.setCreateTime(now);
            assetsBillCoinTypeProperty.setUpdateTime(now);
            return assetsBillCoinTypeProperty;
        }).collect(Collectors.toList());
        assetsBillCoinTypePropertyService.batchInsert(assetsBillCoinTypeProperties, accountType, accountParam);
        assetsBillConfig.setCheckOkTime(new Date(checkTime));
        assetsBillConfig.setConsumeOffset("{}");
        assetsBillConfigService.updateByPrimaryKeySelective(assetsBillConfig);
        AssetsBillConfigSnapshot assetsBillConfigSnapshot = assetsBillConfig.convertToAssetsBillConfigSnapshot();
        assetsBillConfigSnapshotService.batchInsert(Lists.newArrayList(assetsBillConfigSnapshot));
        //AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
        // 查询一下当前初始化时间的待动账，打印到日志里，用于配置apollo
        //List<BillContractProfitTransfer> transfers = assetsCheckBiz.getBillContractProfitTransfers(assetsBillConfig, assetsCheckConfig);
        log.info("Successfully updated inAllInit data, got assetsBillConfig: {} , transfers: {}", JSONObject.toJSONString(assetsBillConfig));

    }


    private void doBaseBillStart(ApolloReconciliationBizConfig apolloBizConfig) {
        // 从apollo获取所有业务线的引擎配置
        if (!EnvUtil.isLocalDebugByAccountType(apolloBizConfig.getAccountType())) {
            if (apolloBizConfig == null || !apolloBizConfig.isOpen() || !apolloBizConfig.isTimeSliceCheckOpen()) {
                log.info("TimeSliceBillCheckService.doBaseBillStart isLocalDebugByAccountType {}", apolloBizConfig.getAccountType());
                return;
            }
        }
        // 串行，所有时间片保持同步推进
        BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(apolloBizConfig.getAccountType());
        if (billLogicGroup == null) {
            log.info("TimeSliceBillCheckService.doBaseBillStart billLogicGroup is null {}", apolloBizConfig.getAccountType());
            return;
        }
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        BillTimeSliceCheckModule timeSliceModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
        BillUserCheckModule userCheckModule = (BillUserCheckModule) billLogicGroup.getUserCheckModule();
        Map<Integer, Long> partitonInfoMap = timeSliceModule.getLatestPartitionBizTime();
        StringBuffer stringBuffer = new StringBuffer();
        for (Map.Entry<Integer, Long> entry : partitonInfoMap.entrySet()) {
            stringBuffer.append(entry.getKey()).append(":").append(DateUtil.longToDate(entry.getValue())).append("  ");
        }
        log.info("TimeSliceBillCheckService.doBaseBillStart latestPartitionBizTime {} {}", apolloBizConfig.getAccountType(), stringBuffer);
        Long latestBizTime = timeSliceModule.getMinPartitionBizTime();
        if (latestBizTime == null) {
            log.error("TimeSliceBillCheckService.doBaseBillStart latestBizTime is null {} {}", apolloBizConfig.getAccountType(), timeSliceModule.getSaveTimeSliceDTOMap().size());
            return;
        }
        SortedMap<Long, BillTimeSliceDTO> startTimeSliceDTOMap = timeSliceModule.getStartTimeSliceDTOMap();
        SortedMap<Long, BillTimeSliceDTO> saveTimeSliceDTOMap = timeSliceModule.getSaveTimeSliceDTOMap();
        // 获取errorMap最小时间 如果小于当前 对账
        Long minErrorMapBizTime = userCheckModule.getMinTimeSliceBillCheckTime();

        BillCheckService checkAssetService = accountAssetsServiceFactory.getBillCheckService(apolloBizConfig.getAccountType());
        // 允许延迟对账时间 最大容忍消息延迟时间
        long delayBillTime = latestBizTime - apolloBizConfig.getBillCheckBeforeBizTimeThreshold();
        int startTimeSliceDTOMapSize = startTimeSliceDTOMap.size();
        for (int i = 0; i < startTimeSliceDTOMapSize; i++) {
            // 判断存储时间片大小 如果如果存储压力过大 停止对账
            if (saveTimeSliceDTOMap.size() > apolloBizConfig.getSaveTimeSliceMapLimitSize()) {
                log.info("TimeSliceBillCheckService.doBaseBillStart timeSliceModule.getSaveTimeSliceDTOMap().size gte {} {}", apolloBizConfig.getAccountType(), timeSliceModule.getSaveTimeSliceDTOMap().size());
                return;
            }
            Long timeSliceKey = startTimeSliceDTOMap.firstKey();
            String timeSliceKeyStr = DateUtil.date2str(new Date(timeSliceKey));
            BillTimeSliceDTO billTimeSliceDTO = startTimeSliceDTOMap.get(timeSliceKey);
            BillTimeSliceDTO lastBillTimeSliceDTO = timeSliceModule.getLastBillTimeSliceDTO();
            BillTimeSliceDTO lastSaveBillTimeSliceDTO = timeSliceModule.getLastSaveBillTimeSliceDTO();
            log.info("TimeSliceBillCheckService.baseBillStart, last bill accountType:{}  key:{} startTimeSliceDTOMapSize:{} config:{} ", accountTypeEnum, timeSliceKeyStr, startTimeSliceDTOMapSize, JSON.toJSONString(lastBillTimeSliceDTO.getBillConfig()));
            if (billTimeSliceDTO == null) {
                return;
            }
            log.info("TimeSliceBillCheckService.baseBillStart lastBillTimeSliceDTO size info accountType {} , current bill config:{}， uniqueMap size {}, CoinUserPropertyMap size {} ,CoinPropertyMap size {}, CoinTypePropertyMap size {}", apolloBizConfig.getAccountType(), JSON.toJSONString(billTimeSliceDTO.getBillConfig()), lastBillTimeSliceDTO.getUniqueMap().size(), lastBillTimeSliceDTO.getCoinUserPropertyMap().size(), lastBillTimeSliceDTO.getCoinPropertyMap().size(), lastBillTimeSliceDTO.getCoinTypePropertyMap().size());
            if (timeSliceKey < lastBillTimeSliceDTO.getBillConfig().getCheckOkTime().getTime()) {
                log.error("TimeSliceBillCheckService.baseBillStart less lastBillTimeSliceDTO {} {} {}", apolloBizConfig.getAccountType(), timeSliceKeyStr, lastBillTimeSliceDTO.getBillConfig().getCheckOkTime().getTime());
                startTimeSliceDTOMap.remove(timeSliceKey);
                continue;
            }
            if (timeSliceKey >= delayBillTime) {
                log.info("TimeSliceBillCheckService.baseBillStart gte delayBillTime {} {} {}", apolloBizConfig.getAccountType(), timeSliceKeyStr, DateUtil.date2str(new Date(delayBillTime)));
                break;
            }
            if (minErrorMapBizTime != null && minErrorMapBizTime < timeSliceKey) {
                log.error("TimeSliceBillCheckService.baseBillStart gte minErrorMapBizTime {} {} {}", apolloBizConfig.getAccountType(), timeSliceKeyStr, DateUtil.date2str(new Date(minErrorMapBizTime)));
                break;
            }
            String saveTimeSliceStatus = (String) redisTemplate.opsForValue().get(RedisUtil.getSaveTimeSliceStatusKey(apolloBizConfig.getAccountType()));
            if (Boolean.FALSE.toString().equals(saveTimeSliceStatus)) {
                log.info("TimeSliceBillCheckService.baseBillStart delay message accountType:{} {} ", apolloBizConfig.getAccountType(), timeSliceKey);
                break;
            }

            boolean needBefore = !TimeSliceCalcUtils.isSaveTimeSlice(lastBillTimeSliceDTO.getBillConfig().getCheckOkTime().getTime(), apolloBizConfig.getTimeSliceSize(), apolloBizConfig.getMergeTimeSliceSize());
            // 合并时间片进行对账
            Tuple2<Boolean, BillTimeSliceDTO> resultTuple = MetricsUtil.histogram(HISTOGRAM_SLICE_BASE_BILL_START + apolloBizConfig.getAccountType(), () -> {
                BillTimeSliceDTO margeBillTimeSliceDTO = BillTimeSliceDTO.merge(lastBillTimeSliceDTO, billTimeSliceDTO, lastSaveBillTimeSliceDTO, new Date(timeSliceKey),
                        needBefore, commonService, noGenerator, accountAssetsServiceFactory, reconSystemAccountService.queryInterestFeeUserId(accountTypeEnum), billCapitalInitPropertyService, reconSystemAccountService);
                boolean result = checkAssetService.timeSliceBillCheck(margeBillTimeSliceDTO, new Date(timeSliceKey), apolloBizConfig, commonService);
                return Tuples.of(result, margeBillTimeSliceDTO);
            });
            Boolean checkResult = resultTuple.getT1();
            BillTimeSliceDTO margeBillTimeSliceDTO = resultTuple.getT2();
            log.info("TimeSliceBillCheckService.baseBillStart error {} {} {} marge bill config: {}", apolloBizConfig.getAccountType(), timeSliceKeyStr, checkResult, JSON.toJSONString(margeBillTimeSliceDTO.getBillConfig()));
            if (!checkResult) {
                break;
            }
            // 对账通过 时间片写入kafka 推送到总账对账， 如果写入报错 重新对账
            sentKafkaTimeSliceToLedger(apolloBizConfig, margeBillTimeSliceDTO);
            // 时间片推进
            startTimeSliceDTOMap.remove(timeSliceKey);
            timeSliceModule.setLastBillTimeSliceDTO(margeBillTimeSliceDTO);
            if (TimeSliceCalcUtils.isSaveTimeSlice(timeSliceKey, apolloBizConfig.getTimeSliceSize(), apolloBizConfig.getMergeTimeSliceSize())) {
                saveTimeSliceDTOMap.put(timeSliceKey, margeBillTimeSliceDTO);
                timeSliceModule.setLastSaveBillTimeSliceDTO(margeBillTimeSliceDTO);
            } else {
                long saveTimeSlice = TimeSliceCalcUtils.getSaveTimeSlice(timeSliceKey, apolloBizConfig.getTimeSliceSize(), apolloBizConfig.getMergeTimeSliceSize());
                startTimeSliceDTOMap.computeIfAbsent(saveTimeSlice, v -> {
                    BillTimeSliceDTO timeSliceDTO = new BillTimeSliceDTO(BillConfig.builder()
                            .checkOkTime(new Date(saveTimeSlice))
                            .consumeOffset(lastBillTimeSliceDTO.getBillConfig().getConsumeOffset())
                            .syncPos(lastBillTimeSliceDTO.getBillConfig().getSyncPos())
                            .build());
                    timeSliceDTO.setAccountTypeEnum(AccountTypeEnum.toEnum(apolloBizConfig.getAccountType()));
                    return timeSliceDTO;
                });
            }
            long nextTimeSlice = TimeSliceCalcUtils.getNextTimeSlice(timeSliceKey, apolloBizConfig.getTimeSliceSize());
            startTimeSliceDTOMap.computeIfAbsent(nextTimeSlice, v -> {
                BillTimeSliceDTO timeSliceDTO = new BillTimeSliceDTO(BillConfig.builder()
                        .checkOkTime(new Date(nextTimeSlice))
                        .consumeOffset(lastBillTimeSliceDTO.getBillConfig().getConsumeOffset())
                        .syncPos(lastBillTimeSliceDTO.getBillConfig().getSyncPos())
                        .build());
                timeSliceDTO.setAccountTypeEnum(AccountTypeEnum.toEnum(apolloBizConfig.getAccountType()));
                return timeSliceDTO;
            });
            monitorSceneContext.runScene(BillConstants.MONITOR_SCENE_FEE_CHECK, new Date(timeSliceKey), apolloBizConfig.getAccountType());
        }
        log.info("TimeSliceBillCheckService.baseBillStart finished accountType:{} startTimeSliceDTOMap size:{}", apolloBizConfig.getAccountType(), startTimeSliceDTOMap.size());
    }

    /**
     * 时间片消息 写入总账Kafka
     *
     * @param apolloBizConfig
     * @param billTimeSliceDTO
     */
    private void sentKafkaTimeSliceToLedger(ApolloReconciliationBizConfig apolloBizConfig, BillTimeSliceDTO billTimeSliceDTO) {
        Date checkOkTime = billTimeSliceDTO.getBillConfig().getCheckOkTime();
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        Collection<BillCoinProperty> billCoinPropertyList = billTimeSliceDTO.getCoinPropertyMap().values();
        Collection<BillCoinTypeProperty> billCoinTypePropertyList = billTimeSliceDTO.getCoinTypePropertyMap().values();
        List<SymbolCheckProperty> symbolCheckPropertyList = billTimeSliceDTO.getSymbolCheckPropertyList();
        List<String> profitTypeList = Lists.newArrayList(ProfitTypeEnum.COIN_PROFIT.getCode(), ProfitTypeEnum.SYMBOL_PROFIT.getCode());
        List<BillContractProfitCoinDetail> contractProfitCoinDetailList = billTimeSliceDTO.getBillContractProfitCoinDetailList().stream().filter(item -> profitTypeList.contains(item.getProfitType())).collect(Collectors.toList());
        List<BillContractProfitSymbolDetail> contractProfitSymbolDetailList = billTimeSliceDTO.getBillContractProfitSymbolDetailList().stream().filter(item -> profitTypeList.contains(item.getProfitType())).collect(Collectors.toList());
        Collection<BillTransferFeeCoinDetail> billTransferFeeCoinDetailList = billTimeSliceDTO.getBillTransferFeeCoinDetailMap().values();
        AssetsKafkaConfig assetsKafkaConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig().getAssetsKafkaConfig();
        AssetsKafkaConsumerConfig kafkaConsumerConfig = assetsKafkaConfig.getKafkaConsumerConfig(String.valueOf(accountTypeEnum.getCode()));
        if (kafkaConsumerConfig == null) {
            return;
        }
        String topic = kafkaConsumerConfig.getTopic();
        log.info("sentKafkaTimeSliceToLedger start accountType:{} checkTime:{} topic:{} billCoinPropertyListSize:{} billCoinTypePropertyListSize:{} contractProfitCoinDetailListSize:{} billTransferFeeCoinDetailListSize:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), topic, billCoinPropertyList.size(), billCoinTypePropertyList.size(), contractProfitCoinDetailList.size(), billTransferFeeCoinDetailList.size());
        // 发送开始消息
        ProducerRecord<String, String> startRecord = new ProducerRecord<>(topic, String.valueOf(checkOkTime.getTime()), JSON.toJSONString(BillCoinTypeProperty.buildLedgerKafkaStartProperty(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkOkTime)));
        // 同步发送开始消息，确保第一个消息发送成功
        try {
            kafkaProducer.send(startRecord).get(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("sentKafkaTimeSliceToLedger start record send error accountType:{} checkTime:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), e);
            throw new RuntimeException("send error");
        }
        // 发送资产数据
        for (BillCoinProperty billCoinProperty : billCoinPropertyList) {
            billCoinProperty.setPropertySubType(PropertySubTypeEnum.COIN.getCode());
            billCoinProperty.setAccountType(accountTypeEnum.getCode());
            billCoinProperty.setAccountParam(accountTypeEnum.getAccountParam());
            ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(topic, String.valueOf(checkOkTime.getTime()), JSON.toJSONString(billCoinProperty));
            kafkaProducer.send(incrementRecord);
        }
        for (BillCoinTypeProperty billCoinTypeProperty : billCoinTypePropertyList) {
            billCoinTypeProperty.setPropertySubType(PropertySubTypeEnum.COIN_TYPE.getCode());
            billCoinTypeProperty.setAccountType(accountTypeEnum.getCode());
            billCoinTypeProperty.setAccountParam(accountTypeEnum.getAccountParam());
            ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(topic, String.valueOf(checkOkTime.getTime()), JSON.toJSONString(billCoinTypeProperty));
            kafkaProducer.send(incrementRecord);
        }
        // 计算总账多空仓和盈亏数据
        if (apolloBizConfig.isSumLedgerPositionCountAndProfitFlag()) {
            for (SymbolCheckProperty symbolCheckProperty : symbolCheckPropertyList) {
                symbolCheckProperty.setPropertySubType(PropertySubTypeEnum.SYMBOL_CHECK.getCode());
                symbolCheckProperty.setAccountType(accountTypeEnum.getCode());
                symbolCheckProperty.setAccountParam(accountTypeEnum.getAccountParam());
                ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(topic, String.valueOf(checkOkTime.getTime()), JSON.toJSONString(symbolCheckProperty));
                kafkaProducer.send(incrementRecord);
            }
        }
        for (BillContractProfitCoinDetail contractProfitCoinDetail : contractProfitCoinDetailList) {
            contractProfitCoinDetail.setPropertySubType(PropertySubTypeEnum.PROFIT_COIN.getCode());
            contractProfitCoinDetail.setAccountType(accountTypeEnum.getCode());
            contractProfitCoinDetail.setAccountParam(accountTypeEnum.getAccountParam());
            ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(topic, String.valueOf(checkOkTime.getTime()), JSON.toJSONString(contractProfitCoinDetail));
            kafkaProducer.send(incrementRecord);
        }
        for (BillContractProfitSymbolDetail contractProfitSymbolDetail : contractProfitSymbolDetailList) {
            contractProfitSymbolDetail.setPropertySubType(PropertySubTypeEnum.PROFIT_SYMBOL.getCode());
            contractProfitSymbolDetail.setAccountType(accountTypeEnum.getCode());
            contractProfitSymbolDetail.setAccountParam(accountTypeEnum.getAccountParam());
            ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(topic, String.valueOf(checkOkTime.getTime()), JSON.toJSONString(contractProfitSymbolDetail));
            kafkaProducer.send(incrementRecord);
        }
        for (BillTransferFeeCoinDetail transferFeeCoinDetail : billTransferFeeCoinDetailList) {
            transferFeeCoinDetail.setPropertySubType(PropertySubTypeEnum.TRANSFER_FEE.getCode());
            transferFeeCoinDetail.setAccountType(accountTypeEnum.getCode());
            transferFeeCoinDetail.setAccountParam(accountTypeEnum.getAccountParam());
            ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(topic, String.valueOf(checkOkTime.getTime()), JSON.toJSONString(transferFeeCoinDetail));
            kafkaProducer.send(incrementRecord);
        }
        // 发送结束消息
        ProducerRecord<String, String> endRecord = new ProducerRecord<>(topic, String.valueOf(checkOkTime.getTime()), JSON.toJSONString(BillCoinTypeProperty.buildLedgerKafkaEndProperty(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkOkTime)));
        // 同步发送结束消息，确保本批次最后一个消息发送成功
        try {
            kafkaProducer.send(endRecord).get(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("sentKafkaTimeSliceToLedger end record send error accountType:{} checkTime:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), e);
            throw new RuntimeException("send error");
        }
        log.info("sentKafkaTimeSliceToLedger end accountType:{} checkTime:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime));
    }

    /**
     * 获取kafka分区
     *
     * @param accountType
     * @param partitionNum
     * @return
     */
    private Integer getLedgerKafkaPartition(Byte accountType, Integer partitionNum) {
        return (int) (ledgerKafkaPartitionCounterMap.computeIfAbsent(accountType, v -> new AtomicLong(0)).getAndIncrement() % partitionNum);
    }


    public void inAllCoinTypeInit(Long checkTime, String accountType, String accountParam) {
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(accountType, accountParam);
        if (assetsBillConfig == null || checkTime == null) {
            return;
        }

        Date now = new Date();
        AssetsBaseRequest assetsBaseRequest = new AssetsBaseRequest();
        assetsBaseRequest.setAssetsCheckType(accountType);
        assetsBaseRequest.setAssetsCheckParam(accountParam);
        assetsBaseRequest.setEndTime(checkTime);

        // asset coin_type 表初始化
        List<AssetsInfoResult> assetsInfoCoinTypeResults = internalAssetsService.queryAllAssetsType(assetsBaseRequest);
        List<AssetsBillCoinTypeProperty> assetsBillCoinTypeProperties = assetsInfoCoinTypeResults.stream().map(item -> {
            AssetsBillCoinTypeProperty assetsBillCoinTypeProperty = BeanCopierUtil.copyProperties(item, AssetsBillCoinTypeProperty.class);
            assetsBillCoinTypeProperty.setCheckTime(new Date(checkTime));
            assetsBillCoinTypeProperty.setCreateTime(now);
            assetsBillCoinTypeProperty.setUpdateTime(now);
            return assetsBillCoinTypeProperty;
        }).collect(Collectors.toList());
        assetsBillCoinTypePropertyService.batchInsert(assetsBillCoinTypeProperties, accountType, accountParam);
        log.info("Successfully updated inAllCoinTypeInit data, got assetsBillConfig: {} , transfers: {}", JSONObject.toJSONString(assetsBillConfig));

    }
}
