package com.upex.reconciliation.service.dao.entity;

import com.upex.mixcontract.common.repo.AbstractEntity;
import com.upex.mixcontract.common.repo.CombinedKeys;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
public class BillCoinUserFlow extends AbstractEntity {


    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 币种
     */
    private Integer coinId;

    /**
     * 合并业务时间
     */
    private Date combineCheckTime;

    /**
     * 资产值1变化
     */
    private BigDecimal changeProp1;

    /**
     * 资产值1
     */
    private BigDecimal prop1;

    /**
     * 资产值2变化
     */
    private BigDecimal changeProp2;

    /**
     * 资产值2
     */
    private BigDecimal prop2;

    /**
     * 资产值3变化
     */
    private BigDecimal changeProp3;

    /**
     * 资产值3
     */
    private BigDecimal prop3;

    /**
     * 资产值4变化
     */
    private BigDecimal changeProp4;

    /**
     * 资产值4
     */
    private BigDecimal prop4;

    /**
     * 资产值5变化
     */
    private BigDecimal changeProp5;

    /**
     * 资产值5
     */
    private BigDecimal prop5;

    /**
     * 资产值6变化
     */
    private BigDecimal changeProp6;

    /**
     * 资产值6
     */
    private BigDecimal prop6;

    /**
     * 资产值7变化
     */
    private BigDecimal changeProp7;

    /**
     * 资产值7
     */
    private BigDecimal prop7;

    /**
     * 资产值8变化
     */
    private BigDecimal changeProp8;

    /**
     * 资产值8
     */
    private BigDecimal prop8;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;


    @Override
    public Long getVersion() {
        return null;
    }

    @Override
    public void setVersion(long version) {

    }

    @Override
    public CombinedKeys getUniqueKey() {
        return null;
    }
}