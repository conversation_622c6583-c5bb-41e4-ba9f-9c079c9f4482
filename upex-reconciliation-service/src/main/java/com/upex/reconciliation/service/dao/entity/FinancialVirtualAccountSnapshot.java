package com.upex.reconciliation.service.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 理财产品快照余额表
 * @TableName financial_virtual_account_snapshot_t0
 */
@Data
public class FinancialVirtualAccountSnapshot implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 币种ID
     */
    private Integer coinId;

    /**
     * 币种名称
     */
    private String coinName;

    /**
     * 业务分组：1-Savings，2-BgbEarn，3-LaunchPool，4-SharkFin，5-DualInvest，6-定期
     */
    private Integer groupType;

    /**
     * 理财总金额
     */
    private BigDecimal totalBalance;

    /**
     * 快照时间
     */
    private Date snapshotTime;

    /**
     * 业务表创建时间
     */
    private Date sourceCreateTime;

    /**
     * 业务表更新时间
     */
    private Date sourceUpdateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 数据版本字段
     */
    private Long version;

    /**
     * 预结利息
     */
    private BigDecimal preSettleInterest;

    private static final long serialVersionUID = 1L;

}