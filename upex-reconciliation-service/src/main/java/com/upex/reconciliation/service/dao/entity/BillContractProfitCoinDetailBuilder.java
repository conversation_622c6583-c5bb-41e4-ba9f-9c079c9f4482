package com.upex.reconciliation.service.dao.entity;

import com.upex.bill.dto.enums.ProfitTransferStatusEnum;
import com.upex.bill.dto.results.AccountAssetsInfoResult ;
import com.upex.reconciliation.service.common.constants.enums.ProfitTypeEnum;
import com.upex.utils.util.SerialNoGenerator;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 币种维度盈亏明细构造器
 * <AUTHOR>
 * @date 2023/8/11 22:23
 */
public class BillContractProfitCoinDetailBuilder {

    private final BillContractProfitCoinDetail billContractProfitDetail = new BillContractProfitCoinDetail();

    public static BillContractProfitCoinDetailBuilder get() {
        return new BillContractProfitCoinDetailBuilder();
    }

    public BillContractProfitCoinDetailBuilder buildGlobalProp(SerialNoGenerator noGenerator, long batchNo) {
        billContractProfitDetail.setId(noGenerator.nextNo());
        // batchNo，一个批次有一个
        billContractProfitDetail.setBatchNo(batchNo);
        return this;
    }

    public BillContractProfitCoinDetailBuilder buildAccountAssetsInfo(AccountAssetsInfoResult item) {
        billContractProfitDetail.setAccountType(item.getAccountType());
        billContractProfitDetail.setAccountParam(item.getAccountParam());
        billContractProfitDetail.setCoinId(item.getCoinId());
        billContractProfitDetail.setProfitCountIncr(item.getProp2());
        return this;
    }

    public BillContractProfitCoinDetailBuilder buildTimeAndToInitCount(BillConfig billConfig, BigDecimal rate) {
        billContractProfitDetail.setCheckOkTime(billConfig.getCheckOkTime());
        billContractProfitDetail.setRealizedCount(BigDecimal.ZERO);
        billContractProfitDetail.setUnrealizedCount(BigDecimal.ZERO);
        billContractProfitDetail.setProfitCount(BigDecimal.ZERO);
        billContractProfitDetail.setRate(rate);
        billContractProfitDetail.setProfitAmount(billContractProfitDetail.getProfitCountIncr().multiply(billContractProfitDetail.getRate()));
        return this;
    }

    public BillContractProfitCoinDetailBuilder buildTypeAndStatus(ProfitTransferStatusEnum profitTransferStatusEnum, ProfitTypeEnum profitTypeEnum) {
        billContractProfitDetail.setStatus(profitTransferStatusEnum.getCode());
        billContractProfitDetail.setProfitType(profitTypeEnum.getCode());
        return this;
    }

    public BillContractProfitCoinDetailBuilder buildBaseTime(Date currentDate) {
        billContractProfitDetail.setCreateTime(currentDate);
        billContractProfitDetail.setUpdateTime(currentDate);
        return this;
    }

    public BillContractProfitCoinDetail create() {
        return billContractProfitDetail;
    }
}
