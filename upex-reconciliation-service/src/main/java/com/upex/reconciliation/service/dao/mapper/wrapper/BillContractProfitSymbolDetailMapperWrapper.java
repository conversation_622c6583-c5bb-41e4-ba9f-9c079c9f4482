package com.upex.reconciliation.service.dao.mapper.wrapper;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillContractProfitSymbolDetail;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitSymbolDetailMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component("billContractProfitSymbolDetailMapperWrapper")
public class BillContractProfitSymbolDetailMapperWrapper implements BillContractProfitSymbolDetailMapper {

    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billContractProfitSymbolDetailMapper")
    private BillContractProfitSymbolDetailMapper billContractProfitSymbolDetailMapper;

    @Override
    public int batchInsert(Byte accountType, String accountParam, List<BillContractProfitSymbolDetail> records) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.batchInsert(accountType, accountParam, records));
    }

    @Override
    public int batchInsertHis(Byte accountType, String accountParam, List<BillContractProfitSymbolDetail> records) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.batchInsertHis(accountType, accountParam, records));
    }

    @Override
    public List<BillContractProfitSymbolDetail> getBillContractProfitSymbols(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.getBillContractProfitSymbols(accountType, accountParam, checkTime));
    }

    @Override
    public List<BillContractProfitSymbolDetail> getAllBillContractProfitSymbols(Byte accountType, String accountParam, Date startTime, Date endTime) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.getAllBillContractProfitSymbols(accountType, accountParam, startTime, endTime));
    }

    @Override
    public int deleteAfterRecord(Date resetCheckTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.deleteAfterRecord(resetCheckTime, accountType, accountParam));
    }

    @Override
    public List<BillContractProfitSymbolDetail> getAllAfterRecord(Date resetCheckTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.getAllAfterRecord(resetCheckTime, accountType, accountParam));
    }

    @Override
    public Date getLastCheckOkTime(Byte accountType, String accountParam, String profitType) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.getLastCheckOkTime(accountType, accountParam, profitType));
    }

    @Override
    public int deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }

    @Override
    public Boolean batchDelete(Long minId, Long deleteSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.batchDelete(minId, deleteSize, accountType, accountParam));
    }

    @Override
    public List<BillContractProfitSymbolDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime));
    }

    @Override
    public List<BillContractProfitSymbolDetail> getAllContractProfitSymbolDetailList(Byte accountType, String accountParam, List<String> profitTypeList, Date checkOkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.getAllContractProfitSymbolDetailList(accountType, accountParam, profitTypeList, checkOkTime));
    }

    @Override
    public int updateById(Byte accountType, String accountParam, BillContractProfitSymbolDetail record) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitSymbolDetailMapper.updateById(accountType, accountParam, record));
    }
}
