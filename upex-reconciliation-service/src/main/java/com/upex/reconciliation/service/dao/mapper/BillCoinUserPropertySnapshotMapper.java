package com.upex.reconciliation.service.dao.mapper;


import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository("billCoinUserPropertySnapshotMapper")
public interface BillCoinUserPropertySnapshotMapper {

    int batchInsert(@Param("records") List<BillCoinUserProperty> records,
                    @Param("accountType") Byte accountType,
                    @Param("accountParam") String accountParam);

    Boolean deleteByCheckTime(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("checkTime") Date checkTime, @Param("pageSize") Long pageSize);


    List<BillCoinUserProperty> selectMaxCheckTime(@Param("accountType") Byte accountType,
                                                  @Param("accountParam") String accountParam,
                                                  @Param("userId") Long userId);

    Boolean updateById(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("data") BillCoinUserProperty data);

    BillCoinUserProperty selectById(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("id") Long id);


    List<BillCoinUserProperty> selectByUserIdsAndCheckTime(@Param("userIds") List<Long> userIds,
                                                           @Param("checkTime") Date checkTime,
                                                           @Param("accountType") Byte accountType,
                                                           @Param("accountParam") String accountParam);

    int updateCheckTimeById(@Param("ids") List<Long> ids,
                            @Param("checkTime") Date checkTime,
                            @Param("updateTime") Date updateTime,
                            @Param("accountType") Byte accountType,
                            @Param("accountParam") String accountParam);


    int batchInsertOrUpdate(@Param("records") List<BillCoinUserProperty> records,
                            @Param("accountType") Byte accountType,
                            @Param("accountParam") String accountParam);

    Boolean batchDelete(@Param("beginId") Long beginId, @Param("pageSize") Long pageSize, @Param("accountType") Byte accountType, @Param("accountParam") String accountParam);


    List<BillCoinUserProperty> selectAllAssetsTiming(@Param("accountType") Integer accountType,
                                                     @Param("accountParam") String accountParam,
                                                     @Param("checkTime") String checkTime);


    List<Long> selectUserIds(@Param("accountType") Integer accountType,
                             @Param("accountParam") String accountParam);


    List<BillCoinUserProperty> selectSysAssetsSnapShotByTime(@Param("userId") Long userId, @Param("checkTime") Date checkTime, @Param("accountType") Integer accountType,
                                                             @Param("accountParam") String accountParam);

    List<BillCoinUserProperty> selectSingleUser(@Param("userId") Long userId,
                                                @Param("coinId") Integer coinId,
                                                @Param("accountType") Integer accountType,
                                                @Param("accountParam") String accountParam,
                                                @Param("checkTime") Date checkTime);

    /**
     * 根据对帐时间获取用户的所有币种流水记录，可以后续添加币种作为参数
     *
     * @param userId
     * @param checkTime
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillCoinUserProperty> selectInfosByCheckTime(@Param("userId") Long userId,
                                                      @Param("checkTime") Date checkTime,
                                                      @Param("accountType") Integer accountType,
                                                      @Param("accountParam") String accountParam);

    /**
     * 获取最近次对帐时间
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    BillCoinUserProperty selectTime(@Param("userId") Long userId,
                                    @Param("accountType") Integer accountType,
                                    @Param("accountParam") String accountParam,
                                    @Param("checkTime") Date checkTime);

    /**
     * 删除初始化时间在重置对账时间之后的记录
     *
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int deleteInitialTimeAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                                     @Param("accountType") Integer accountType,
                                     @Param("accountParam") String accountParam);

    /**
     * 更新重置对账时间在初始化时间和最后对账时间之间的记录
     *
     * @param userId
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int updateInitAndCheckBetweenRecord(@Param("userId") Long userId, @Param("lastCheckTime") Date lastCheckTime, @Param("resetCheckTime") Date resetCheckTime,
                                        @Param("accountType") Integer accountType,
                                        @Param("accountParam") String accountParam);

    /**
     * 获取用户的快照时间的所有币种的对帐记录；
     * 参数取等号
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    List<BillCoinUserProperty> selectRecordsByUserIdAndCheckTime(
            @Param("userId") Long userId,
            @Param("accountType") String accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime);

    /**
     * 获取用户下不同币种的时间快照
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    BillCoinUserProperty selectRecordLimitByUserCoin(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime
    );

    /**
     * 获取用户下币种的快照记录（对帐时间一直更新，初始化时间不变）
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    BillCoinUserProperty selectCheckTimeOverInitLimitByUerCoin(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime);

    /**
     * 获取对帐时间记录的的所有币种
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    List<BillCoinUserProperty> selectCoinsByUserAndTime(
            @Param("userId") Long userId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime
    );

    /**
     * 删除历史记录
     *
     * @param ids          主键集合
     * @param accountType  账户类型
     * @param accountParam 账户参数
     * @return
     */
    int deleteUserCoinHistoryRecord(@Param("accountType") String accountType,
                                    @Param("accountParam") String accountParam,
                                    @Param("ids") List<Long> ids);

//    /**
//     * 查询历史记录
//     *
//     * @param queryUserDTO 查询用户资产dto 根据用户分组的数据查询
//     * @return
//     */
//    List<Long> selectUserCoinHistoryRecord(QueryUserDTO queryUserDTO);

    /**
     * 获取指定时间范围的数据
     *
     * @param accountType
     * @param accountParam
     * @param startTime
     * @param endTime
     * @param pageSize
     * @return
     */
    List<BillCoinUserProperty> selectRangeCheckTimeRecordPage(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("minId") Long minId,
            @Param("pageSize") Long pageSize);


    List<BillCoinUserProperty> selectCheckTimeRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime,
            @Param("beginId") Long beginId,
            @Param("pageSize") Long pageSize);


    BillCoinUserProperty selectCoinUserBeforeCheckTimeRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("coinId") Integer coinId,
            @Param("userId") Long userId,
            @Param("checkTime") Date checkTime);

    List<BillCoinUserProperty> selectAllCoinUserByCheckTimeAndCoinIds(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("checkTime") Date checkTime);


    BillCoinUserProperty selectCoinUserLatest(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("coinId") Integer coinId,
            @Param("userId") Long userId);


    BillCoinUserProperty selectCoinUserAfterCheckTimeRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("coinId") Integer coinId,
            @Param("userId") Long userId,
            @Param("checkTime") Date checkTime);

    List<BillCoinUserProperty> selectRangeCheckTimeRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);


    List<BillCoinUserProperty> selectUserLatestRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId);


    List<BillCoinUserProperty> selectAssetListByUserId(@Param("userId") Long userId,
                                                       @Param("accountType") Byte accountType,
                                                       @Param("accountParam") String accountParam);

    /**
     * 查询最新的用户资产
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @return {@link List< BillCoinUserProperty> }
     * <AUTHOR>
     * @date 2023/4/30 01:15
     */
    List<BillCoinUserProperty> selectLastUserAssetByUserId(@Param("userId") Long userId,
                                                           @Param("accountType") Byte accountType,
                                                           @Param("accountParam") String accountParam);

    /**
     * 查询资产
     *
     * @param checkTime
     * @param coinId
     * @param uids
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillCoinUserProperty> selectByIds(
            @Param("checkTime") Date checkTime,
            @Param("coinId") Integer coinId,
            @Param("uids") List<Long> uids,
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam
    );

    /**
     * 获取时间片id
     *
     * @param checkTime
     * @return
     */
    Long getIdByCheckTime(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("checkTime") Date checkTime,
                          @Param("operation") String operation);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param batchSize
     * @return
     */
    Boolean deleteByMaxId(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("maxId") Long maxId,
                          @Param("batchSize") Long batchSize);

    /**
     * 查询用户快照资产
     *
     * @param accountType
     * @param accountParam
     * @param userId
     * @param coinIds
     * @param checkTime
     * @return
     */
    List<BillCoinUserProperty> selectCoinUserSnapshotAsset(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinIds") List<Integer> coinIds,
            @Param("checkTime") Date checkTime);

    /**
     * 更新快照资产
     *
     * @param accountType
     * @param accountParam
     * @param record
     * @return
     */
    boolean updateSelectiveById(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("record") BillCoinUserProperty record);

    /**
     * 查询资产快照
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param minId
     * @param maxId
     * @return
     */
    List<BillCoinUserProperty> selectCoinUserSnapshotByIdSegment(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime,
            @Param("minId") Long minId,
            @Param("maxId") Long maxId);

    /**
     * 查询最大id
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    Long selectMaxId(@Param("accountType") Byte accountType,
                     @Param("accountParam") String accountParam,
                     @Param("checkTime") Date checkTime);

    /**
     * 查询最小id
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    Long selectMinId(@Param("accountType") Byte accountType,
                     @Param("accountParam") String accountParam,
                     @Param("checkTime") Date checkTime);

    /**
     * 查询用户资产
     *
     * @param accountType
     * @param accountParam
     * @param userId
     * @param coinId
     * @param checkTime
     * @return
     */
    BillCoinUserProperty selectLastUserAssetByUserCoinId(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("checkTime") Date checkTime);

    /**
     * 获取历史用户资产
     *
     * @param accountType
     * @param accountParam
     * @param userId
     * @param coinId
     * @param checkTime
     * @return
     */
    List<BillCoinUserProperty> selectCoinUserLtCheckTime(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("checkTime") Date checkTime);

    /**
     * 批量删除数据
     *
     * @param accountType
     * @param accountParam
     * @param coinUserIds
     * @return
     */
    Boolean deleteByIds(@Param("accountType") Byte accountType,
                        @Param("accountParam") String accountParam,
                        @Param("coinUserIds") List<Long> coinUserIds);

    /**
     * 更新用户资产
     *
     * @param accountType
     * @param accountParam
     * @param userId
     * @param coinId
     * @param checkTime
     * @param billCoinUserProperty
     * @return
     */
    Integer updateSpropByUserCoinCheckTime(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("checkTime") Date checkTime,
            @Param("record") BillCoinUserProperty billCoinUserProperty);
}