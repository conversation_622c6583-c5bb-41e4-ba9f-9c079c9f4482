package com.upex.reconciliation.service.model.dto;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 实时盈亏计算时间片
 */
@Slf4j
@Data
public class BillProfitTimeSliceDTO {
    /***业务线***/
    private AccountTypeEnum accountTypeEnum;
    /***当前时间片时间***/
    private Long timeSlice;
    /***用户-》币种-》盈亏***/
    private Map<Long, Map<Integer, BigDecimal>> userCoinProfitMap = new ConcurrentHashMap<>();
    /***用户-》币种-交易类型-》金额***/
    private Map<Long, Map<Integer, Map<String, BigDecimal>>> userCoinBizTypeProfitMap = new ConcurrentHashMap<>();

    public BillProfitTimeSliceDTO(AccountTypeEnum accountTypeEnum, Long timeSlice) {
        this.accountTypeEnum = accountTypeEnum;
        this.timeSlice = timeSlice;
    }
}