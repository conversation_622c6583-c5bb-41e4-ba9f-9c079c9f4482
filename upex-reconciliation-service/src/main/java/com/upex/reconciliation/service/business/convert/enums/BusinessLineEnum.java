package com.upex.reconciliation.service.business.convert.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务线枚举
 * 定义不同业务线及其对应的account_type映射关系
 */
@Getter
@AllArgsConstructor
public enum BusinessLineEnum {

    /**
     * SPOT 用户闪兑现货业务线
     * 包含：用户闪兑(1)
     */
    USER_CONVERT_SPOT("user_convert_spot", "用户现货闪兑"),

    /**
     * SPOT 系统闪兑现货业务线
     * 包含：质押借贷-强平手续费风险入账/借贷穿仓扣减出账(10)、
     * 质押借贷爆仓兑换账户(11)、机构借贷-强平兑换入/出(12)、
     * 机构借贷-强平手续费/强平穿仓补偿风险金(13)、小额兑换(-1)
     */
    SYS_CONVERT_SPOT("sys_convert_spot", "系统现货闪兑"),

    /**
     * MARGIN 闪兑杠杆业务线
     * 包含：爆仓兑换账户(2)、手续费归集账户(8)、杠杆风险基金账户(9)
     */
    CONVERT_MARGIN("convert_margin", "杠杆业务线"),

    /**
     * CONTRACT 合约业务线
     * 包含：兑换系统账户(3)、手续费账号(5)、资金费用账号(6)、负债风险基金账户(7)
     */
    CONVERT_CONTRACT("convert_contract", "合约业务线"),

    /**
     * UTA 统一账户业务线
     * 包含：统一账户-小额资产承兑系统账号(4)、统一账户-兑换系统账户(14)、
     * 统一账户-交易手续费系统账户(15)、统一账户-默认系统账号(16)、
     * 统一账户-强平风险基金系统账户(17)
     */
    CONVERT_UTA("convert_uta", "统一账户业务线");

    private final String code;
    private final String description;


    /**
     * 根据业务线代码获取业务线枚举
     *
     * @param code 业务线代码
     * @return 业务线枚举
     */
    public static BusinessLineEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BusinessLineEnum businessLine : values()) {
            if (businessLine.getCode().equals(code)) {
                return businessLine;
            }
        }
        return null;
    }

//    /**
//     * 获取所有业务线的account_type映射
//     *
//     * @return Map<Integer, BusinessLineEnum>
//     */
//    public static Map<Integer, BusinessLineEnum> getAccountTypeMapping() {
//        return Arrays.stream(values())
//                .flatMap(businessLine -> businessLine.getAccountTypes().stream()
//                        .map(accountType -> Map.entry(accountType, businessLine)))
//                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
//    }

//    /**
//     * 检查account_type是否属于指定业务线
//     *
//     * @param accountType  account_type
//     * @param businessLine 业务线
//     * @return 是否属于
//     */
//    public static boolean belongsTo(Integer accountType, BusinessLineEnum businessLine) {
//        return businessLine.getAccountTypes().contains(accountType);
//    }

    /**
     * 判断是否为闪兑相关业务线
     * 所有业务线都可能涉及闪兑业务
     */
    public boolean isConvertRelated() {
        return true;
    }

    /**
     * 判断是否为用户闪兑（account_type = 1）
     */
    public boolean isUserConvert(Integer accountType) {
        return accountType != null && accountType.equals(1) && this == SYS_CONVERT_SPOT;
    }

    /**
     * 获取业务线标识
     */
    public String getBusinessLineKey() {
        return code;
    }
}
