package com.upex.reconciliation.service.dao.mapper.wrapper;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.reconciliation.service.dao.mapper.BillCoinUserAssetsMapper;
import com.upex.reconciliation.service.model.domain.PageIdResponse;
import com.upex.reconciliation.service.model.domain.QueryUserDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/22 15:54
 */
@Component("billCoinUserAssetsMapperWrapper")
public class BillCoinUserAssetsMapperWrapper implements BillCoinUserAssetsMapper {

    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "billCoinUserAssetsMapper")
    private BillCoinUserAssetsMapper billCoinUserAssetsMapper;

    @Override
    public int batchInsert(List<BillCoinUserAssets> list, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.batchInsert(list, accountType, accountParam));
    }

    @Override
    public Date selectLastCheckTime(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectLastCheckTime(accountType, accountParam));
    }

    @Override
    public int updateCheckTimeById(List<Long> ids, Date checkTime, Date updateTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.updateCheckTimeById(ids, checkTime, updateTime, accountType,accountParam));
    }

    @Override
    public List<BillCoinUserAssets> selectSysAssetsSnapShotByTime(Long userId, Integer coinId, Date requestTime, Integer accountType, String accountParam, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectSysAssetsSnapShotByTime(userId,coinId,requestTime,accountType,accountParam,isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectSysAssetsSnapShotByTime(userId,coinId,requestTime,accountType,accountParam,isMaster));
    }

    @Override
    public List<BillCoinUserAssets> selectAllAssetsTiming(Integer accountType, String accountParam, String checkTime) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectAllAssetsTiming(accountType,accountParam,checkTime));
    }

    @Override
    public List<BillCoinUserAssets> selectAllAssetsTimingSwapMain(Integer accountType, String accountParam, String checkTime) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectAllAssetsTimingSwapMain(accountType,accountParam,checkTime));
    }

    @Override
    public BillCoinUserAssets selectTime(Integer accountType, String accountParam, Long userId, Date checkOkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectTime(accountType,accountParam,userId,checkOkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectTime(accountType,accountParam,userId,checkOkTime, isMaster));
    }

    @Override
    public int deleteInitialTimeAfterRecord(Date resetCheckTime, Integer accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.deleteInitialTimeAfterRecord(resetCheckTime,accountType,accountParam));
    }

    @Override
    public int updateInitAndCheckBetweenRecord(Date lastCheckTime, Date resetCheckTime, Integer accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.updateInitAndCheckBetweenRecord(lastCheckTime,resetCheckTime,accountType,accountParam));
    }

    @Override
    public List<BillCoinUserAssets> selectSingleByParamAndTime(Long userId, Integer coinId, Integer accountType, String accountParam, Date checkOkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectSingleByParamAndTime(userId,coinId,accountType,accountParam,checkOkTime,isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectSingleByParamAndTime(userId,coinId,accountType,accountParam,checkOkTime,isMaster));
    }

    @Override
    public List<BillCoinUserAssets> selectSingleByParamAndTimeSwapMain(Long userId, Integer coinId, Integer accountType, String accountParam, Date checkOkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectSingleByParamAndTimeSwapMain(userId,coinId,accountType,accountParam,checkOkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectSingleByParamAndTimeSwapMain(userId,coinId,accountType,accountParam,checkOkTime, isMaster));
    }

    @Override
    public BillCoinUserAssets selectRecentNearSnapTime(Integer accountType, String accountParam, Long userId, Date checkOkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecentNearSnapTime(accountType,accountParam,userId,checkOkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecentNearSnapTime(accountType,accountParam,userId,checkOkTime, isMaster));
    }

    @Override
    public List<BillCoinUserAssets> selectRecordsByUserIdAndCheckTime(Long userId, Integer accountType, String accountParam, Date checkOkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordsByUserIdAndCheckTime(userId,accountType,accountParam,checkOkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordsByUserIdAndCheckTime(userId,accountType,accountParam,checkOkTime, isMaster));
    }

    @Override
    public List<BillCoinUserAssets> selectRecordsByUserIdAndTime(Long userId, Integer accountType, String accountParam, Date checkOkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordsByUserIdAndTime(userId,accountType,accountParam,checkOkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordsByUserIdAndTime(userId,accountType,accountParam,checkOkTime, isMaster));
    }

    @Override
    public List<BillCoinUserAssets> selectRecordsByUserIdsAndTime(List<Long> userIds, Byte accountType, String accountParam, Date checkOkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordsByUserIdsAndTime(userIds, accountType,accountParam,checkOkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordsByUserIdsAndTime(userIds, accountType,accountParam,checkOkTime, isMaster));    }

    @Override
    public BillCoinUserAssets selectRecordLimitByUserCoin(Long userId, Integer coinId, Integer accountType, String accountParam, Date checkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordLimitByUserCoin(userId,coinId,accountType,accountParam,checkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordLimitByUserCoin(userId,coinId,accountType,accountParam,checkTime, isMaster));
    }

    @Override
    public BillCoinUserAssets selectRecordLimitByUserCoinSwapMain(Long userId, Integer coinId, Integer accountType, String accountParam, Date checkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordLimitByUserCoinSwapMain(userId,coinId,accountType,accountParam,checkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordLimitByUserCoinSwapMain(userId,coinId,accountType,accountParam,checkTime, isMaster));
    }

    @Override
    public BillCoinUserAssets selectCheckTimeOverInitLimitByUerCoin(Long userId, Integer coinId, Integer accountType, String accountParam, Date checkTime, Boolean isMaster) {
        if (isMaster) {
            return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectCheckTimeOverInitLimitByUerCoin(userId,coinId,accountType,accountParam,checkTime, isMaster));
        }
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectCheckTimeOverInitLimitByUerCoin(userId,coinId,accountType,accountParam,checkTime, isMaster));
    }

    @Override
    public BillCoinUserAssets selectCheckTimeOverInitLimitByUerCoinSwapMain(Long userId, Integer coinId, Integer accountType, String accountParam, Date checkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectCheckTimeOverInitLimitByUerCoinSwapMain(userId,coinId,accountType,accountParam,checkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectCheckTimeOverInitLimitByUerCoinSwapMain(userId,coinId,accountType,accountParam,checkTime, isMaster));
    }

    @Override
    public BillCoinUserAssets selectRecordsByUserLimit(Long userId, Integer accountType, String accountParam, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordsByUserLimit(userId,accountType,accountParam, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectRecordsByUserLimit(userId,accountType,accountParam, isMaster));
    }

    @Override
    public List<BillCoinUserAssets> selectCoinsByUserAndTime(Long userId, Integer accountType, String accountParam, Date checkTime, Boolean isMaster) {
        if (isMaster) {
            return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectCoinsByUserAndTime(userId,accountType,accountParam,checkTime, isMaster));
        }
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectCoinsByUserAndTime(userId,accountType,accountParam,checkTime, isMaster));
    }

    @Override
    public List<Long> selectUsersWithPosition(Integer accountType, String accountParam, Long minId, Long maxId) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectUsersWithPosition(accountType,accountParam,minId,maxId));
    }

    @Override
    public List<Long> selectPositionUsersBetweenIdAndTime(Integer accountType, String accountParam, Long minId, Date initialTime) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectPositionUsersBetweenIdAndTime(accountType,accountParam,minId,initialTime));
    }

    @Override
    public int deleteByIds(List<Long> ids, String accountType, String accountParam) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.deleteByIds(ids,accountType,accountParam));
    }

    @Override
    public Long selectMaxId(Integer accountType, String accountParam) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectMaxId(accountType,accountParam));
    }

    @Override
    public List<BillCoinUserAssets> selectByUserAndTime(Long userId, Date checkOkTime, String accountType, String accountParam) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectByUserAndTime(userId,checkOkTime,accountType,accountParam));
    }

    @Override
    public List<BillCoinUserAssets> selectByUserAndCheckTime(Long userId, Date checkOkTime, String accountType, String accountParam) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectByUserAndCheckTime(userId,checkOkTime,accountType,accountParam));
    }

    @Override
    public int deleteUserCoinAssetsHistoryRecord(List<Long> ids, String accountType, String accountParam) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.deleteUserCoinAssetsHistoryRecord(ids,accountType,accountParam));
    }

    @Override
    public List<Long> selectUserCoinAssetsHistoryRecord(QueryUserDTO queryUserDTO) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectUserCoinAssetsHistoryRecord(queryUserDTO));
    }

    @Override
    public List<BillCoinUserAssets> queryList(Byte accountType, String accountParam, Long maxId, Integer pageSize) {
        return  dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.queryList(accountType,accountParam,maxId,pageSize));
    }

    @Override
    public int batchInsertBackup(List<BillCoinUserAssets> list, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.batchInsertBackup(list,accountType,accountParam));
    }

    @Override
    public List<BillCoinUserAssets> queryByCheckTimeAndParam(Date checkTime, Byte accountType, String accountParam, Long maxId, Integer pageSize) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.queryByCheckTimeAndParam(checkTime,accountType,accountParam,maxId,pageSize));
    }

    @Override
    public Long selectSyncMaxId(Integer accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectSyncMaxId(accountType,accountParam));
    }

    @Override
    public List<BillCoinUserAssets> queryByCheckTimeList(Date checkTime, Byte accountType, String accountParam, Long maxId, Integer pageSize) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.queryByCheckTimeList(checkTime,accountType,accountParam,maxId,pageSize));
    }

    @Override
    public PageIdResponse selectByCheckTimeMaxMinId(Date checkTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectByCheckTimeMaxMinId(checkTime,accountType,accountParam));
    }

    @Override
    public List<BillCoinUserAssets> queryByCheckTimeSliceList(Date checkTime, Byte accountType, String accountParam, Long minId, Long maxId) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.queryByCheckTimeSliceList(checkTime,accountType,accountParam,minId,maxId));
    }

    @Override
    public BillCoinUserAssets selectUserAssetsByUserIdAndCoinId(Byte accountType, String accountParam, Long userId, Integer coinId, boolean isMaster) {
        if(isMaster) {
            return dbHelper.doDbOpInBillMaster(() -> billCoinUserAssetsMapper.selectUserAssetsByUserIdAndCoinId(accountType, accountParam, userId, coinId, isMaster));
        }
        return dbHelper.doDbOpInBillMaster(() -> billCoinUserAssetsMapper.selectUserAssetsByUserIdAndCoinId(accountType, accountParam, userId, coinId, isMaster));
    }

    @Override
    public List<BillCoinUserAssets> selectLastUserAssets(Long userId, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(()-> billCoinUserAssetsMapper.selectLastUserAssets(userId,accountType,accountParam));
    }
}
