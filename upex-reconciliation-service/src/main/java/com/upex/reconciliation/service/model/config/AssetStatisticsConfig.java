package com.upex.reconciliation.service.model.config;

import com.google.common.collect.Lists;
import com.upex.bill.dto.enums.TotalAssetsEnum;
import com.upex.bill.dto.results.AssetsCoinVo;
import com.upex.reconciliation.service.common.constants.BillConstants;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/10/26 下午6:01
 * @Description
 */
@Data
public class AssetStatisticsConfig {
    /**
     * 开关
     */
    private boolean execute = false;
    /**
     * 是否只执行一次
     */
    private boolean executeOneTime = false;
    /**
     * 是否每天执行开关
     */
    private boolean executeEachDay = false;
    /**
     * 重新统计
     */
    private boolean resetStatistics = false;
    /**
     * 开启系统账户（废弃、不可用）的日志
     */
    private boolean showSysAbandonLogs = false;
    /**
     * 开启平台资产混合合约单独计算日志
     */
    private boolean showPlatformMixLogs = false;
    /**
     * 开启系统账户统计时的详细日志（用户+币种维度）
     */
    private boolean showSysAssetsLogs = false;
    /**
     * 资金报警开关
     */
    private Boolean assetsAlarmOpen = true;
    /**
     * 混合合约用户资产对比持久化开关
     */
    private Boolean mixUserAssetDiffOpen = false;
    /**
     * 平台混合合约资产业务线入库的业务线
     */
    private List<Integer> platformMixAccounts = new ArrayList<>();
    /**
     * 平台资金差异日志开关
     */
    private Boolean platformAssetDiffLogOpen = false;
    /**
     * 资产统计开关
     */
    private Map<String, Boolean> assetsOpenConfig;
    /**
     * 指定统计起始时间
     */
    private Long statisticsStartTime;
    /**
     * 获取统计子系统列表
     */
    private List<String> subSystemList;
    /**
     * 统计时间间隔
     */
    private Long statisticsInternal;
    /**
     * 虚增资产起始时间(2021/09/28 00:00:00)
     * 虚增资产起始时间(2022/04/07 00:00:00)
     * 虚增资产起始时间(2022/11/12 00:00:00)
     */
    private Long virtualAddAssetsStartTime;
    /**
     * 虚增资产初始值
     */
    private List<AssetsCoinVo> virtualAddAssetsInitValue;

    /**
     * 虚增资产初始值_最新，以这个
     */
    private List<AssetsCoinVo> newVirtualAddAssetsInitValue;

    /**
     * 挪用资产列表
     */
    private List<AppropriateAssetsModel> appropriateAssetsList;

    /**
     * 系统资产排除用户类型集合
     */
    private List<String> systemAssetsExcludeUserTypeLists;

    /**
     * 平台资产排除用户类型集合
     */
    private List<String> platformAssetsExcludeUserTypeLists;

    /**
     * 虚增资产用户类型集合
     */
    private List<String> virtualAssetsLists;

    private boolean insertOpen = true;

    /**
     * 接口请求失败重试次数
     */
    private Integer reTryNumber = 3;

    /**
     * 接口请求失败休眠时间
     */
    private Integer sleepTime = 5;

    /**
     * 统计时价格为0的币种列表
     */
    private List<String> zeroPriceCoinList = new ArrayList<>();
    /**
     * 最大每页查询记录条数，默认：5000
     */
    private Integer maxPageSize = 5000;
    /**
     * 资金对账不平时重试次数，默认：2
     */
    private Integer tryAgain = 2;
    /**
     * 统计延迟时间
     */
    private Integer delayStatisticsTime = BillConstants.NEG_ONE;
    /**
     * 外部资产类型集合
     */
    private List<Integer> otherAssetsTypeList = Lists.newArrayList(5, 7, 8, 9, 10, 13, 14, 15, 16);
    /**
     * 日志打印资产类型集合
     */
    private List<Integer> logAssetsTypeList = Lists.newArrayList(2, 3, 5, 7, 10, 13, 15);

    /**
     * 表达式
     */
    private String expression;

    /**
     * 错误页大小
     */
    private Integer errorPageSize = 3;

    /**
     * 排错资产查询类型集合
     */
    private List<Integer> errorAssetTypeList = Lists.newArrayList(1, 2, 3, 5, 7, 10, 15, 16, 17, 18);

    /**
     * 对账资产查询类型集合
     */
    private List<Integer> billAssetTypeList = Lists.newArrayList(1, 2, 3, 17, 18);

    /**
     * 24小时报警开关
     * 默认开启，钱包延迟太多可以关闭
     */
    private boolean twentyFourHourAlarmOpen = true;
    /**
     * 查询现货净充值开关
     */
    private boolean queryNetRechargeOpen = false;
    /**
     * 资金对账错误信息明细开关
     */
    private boolean errorInfoDetailOpen = false;

    public boolean isOpen(TotalAssetsEnum totalAssetsEnum) {
        return BooleanUtils.isTrue(getAssetsOpenConfig().get(totalAssetsEnum.toString()));
    }

}
