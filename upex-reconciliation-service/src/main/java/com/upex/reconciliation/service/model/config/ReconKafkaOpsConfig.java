package com.upex.reconciliation.service.model.config;

import lombok.Data;

@Data
public class ReconKafkaOpsConfig {
    // kafka消息消费的空跑开关，开启后，kafka消息接收后，不走到业务逻辑里，直接空跑后跳过
    private boolean kafkaConsumeSkipBusinessLogic = false;
    /***跳过更新消息***/
    private boolean kafkaConsumeSkipUpdateMessage = false;

    // kafka消息消费的空跑开关，开启后，kafka消息接收后，不走到业务逻辑里，直接空跑后跳过
    private boolean kafkaConsumeSkipCapitalOrder = true;

    // 影响是否进行offset重置
    private boolean resetKafkaOffsetOpen = true;

    /**
     * kafka单次获取消息的批处理数量
     */
    private Integer kafkaBatchCount = 50;

    /**
     * kafka topic
     */
    private String topic;

    /**
     * kafka 消费组
     */
    private String groupId = "upex-reconciliation-test7-jason1";


    /**
     * 业务线生产端mq的产生速率
     */
    private Long mqProducerSpeed = 50L;

    /***mq消息tps限制***/
    private Integer mqConsumerRateLimit = 300;

    /**
     * kafka消息的限流公式的常数共因子
     * mqConsumerRateCalFactor/(mqConsumerRateCalFactor + 快了多少分钟)  = 最终分得的速率比值
     */
    private Double mqConsumerRateCalFactor = 30.0;

    /**
     * kafka分区数量
     */
    private Integer kafkaPartitionNum = 1;

    /**
     * kafka消息延时报警的阈值，即kafka消息时间和业务时间大于该值，判定为异常
     */
    private long kafkaMessageBizTimeAndCreateTimeGapThreshold = 5 * 60 * 1000;

    /**
     * 时间片堆积达到该百分比时，开启针对最快的partition进行限流
     */
    private Double fastestPartitionTrafficLimitThreshold = 0.8;

    /**
     * 针对最快的partition进行限流的流速
     */
    private Double fastestPartitionTrafficLimitRate = 1.0;
    /***消息bizTime和startTimeSlice队列最小时间gap值，超过一定数量 则当前partition限流***/
    private Long timeSliceRateLimitBizTimeGap = 60 * 60 * 1000L;
    /***错误队列限流数***/
    private Integer errorBillMapLimitSize = 1000;
    /***加载用户等待队列限流数***/
    private Integer waitLoadUserAssetsMapLimitSize = 500;
    /***热加载用户批处理size***/
    private Integer propertyInitModuleBatchSize = 10;
    /***异步加载用户***/
    private Boolean syncPropertyInit = false;
}
