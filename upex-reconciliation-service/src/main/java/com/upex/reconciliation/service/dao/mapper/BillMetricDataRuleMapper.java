package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillMetricDataRule;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-15
 */
@Repository("billMetricDataRuleMapper")
public interface BillMetricDataRuleMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BillMetricDataRule record);

    int insertSelective(BillMetricDataRule record);


    BillMetricDataRule selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillMetricDataRule record);

    int updateByPrimaryKey(BillMetricDataRule record);
}