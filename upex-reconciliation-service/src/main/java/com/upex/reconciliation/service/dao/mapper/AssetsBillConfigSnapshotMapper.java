package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.AssetsBillConfigSnapshot;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository("assetsBillConfigSnapshotMapper")
public interface AssetsBillConfigSnapshotMapper {
    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("records") List<AssetsBillConfigSnapshot> list);

    /**
     * 查询最后一条数据
     *
     * @param assetsCheckType
     * @param assetsCheckParam
     * @return
     */
    AssetsBillConfigSnapshot selectLastByTypeAndParam(@Param("assetsCheckType") String assetsCheckType,
                                                      @Param("assetsCheckParam") String assetsCheckParam);

    /**
     * 删除数据
     *
     * @param assetsCheckType
     * @param assetsCheckParam
     * @return
     */
    int deleteAll(@Param("assetsCheckType") String assetsCheckType,
                  @Param("assetsCheckParam") String assetsCheckParam);

    boolean deleteByCheckTime(@Param("assetAccountType") String assetAccountType,
                              @Param("assetAccountParam") String assetAccountParam,
                              @Param("inAllRollbackTime") Date inAllRollbackTime);

    boolean deleteByLtCheckTime(@Param("assetAccountType") String assetAccountType,
                                 @Param("assetAccountParam") String assetAccountParam,
                                 @Param("inAllRollbackTime") Date inAllRollbackTime,
                                 @Param("batchSize") Long batchSize);
}