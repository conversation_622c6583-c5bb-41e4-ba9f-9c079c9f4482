package com.upex.reconciliation.service.dao.mapper.wrapper;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.StatisticsProperty;
import com.upex.reconciliation.service.dao.mapper.StatisticsPropertyMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;


@Service
public class StatisticsPropertyService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "statisticsPropertyMapper")
    private StatisticsPropertyMapper statisticsPropertyMapper;

    /**
     * 获取最后一次统计完成并且资产正常的记录
     * @return
     */
    public StatisticsProperty selectLastDoneAndCorrect(){
        return dbHelper.doDbOpInSnapshotGlobalMaster(() -> statisticsPropertyMapper.selectLastDoneAndCorrect());

    }


    /**
     * 获取统计的第一条数据
     * @return
     */
    public StatisticsProperty selectFirstDone(){
        return dbHelper.doDbOpInSnapshotGlobalMaster(() -> statisticsPropertyMapper.selectFirstDone());
    }


    public StatisticsProperty selectOneNearSnapshotTimeAndStatus(Integer status, Date snapshotTime) {
        return dbHelper.doDbOpInSnapshotGlobalMaster(() -> statisticsPropertyMapper.selectOneNearSnapshotTimeAndStatus(status, snapshotTime));
    }

}
