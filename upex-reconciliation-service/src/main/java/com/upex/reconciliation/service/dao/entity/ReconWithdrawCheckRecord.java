package com.upex.reconciliation.service.dao.entity;

import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 提币入参、响应记录实体类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReconWithdrawCheckRecord implements Serializable {

    /**
     * 自增ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * @see ReconCheckResultsParams#getOrderId()
     */
    private Long orderId;

    private String traceId;
    /**
     * @see ReconCheckResultsParams#getRecordCode()
     */
    private String recordCode;

    /**
     * @see ReconWithdrawCheckRecord#businessSource
     */
    private String businessSource;

    /**
     * //@see ReconBillUserVo#isPass
     *  0:失败 1:成功
     */

    private Integer isPass;
    /**
     * //@see WithdrawCheckResultEnum
     * 提币失败原因code
     */

    private Integer withdrawCheckResultCode;
    /**
     * //@see WithdrawCheckResultEnum
     * 提币失败原因
     */

    private String withdrawCheckResultName;
    /**
     * 请求时间
     */

    private Date requestTime;
    /**
     * 响应时间
     */

    private Date responseTime;

    private Date checkOnTime;

    private Date createTime;

    private Date updateTime;





}
