package com.upex.reconciliation.service.dao.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProfitLogDTO {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 业务类型
     */
    private String bizType;
    /**
     * 币种id
     */
    private Integer coinId;
    /**
     * 数量
     */
    private BigDecimal count;
    /**
     * 汇率
     */
    private BigDecimal rate;
    /**
     * 折u数量
     */
    private BigDecimal uAmount;
    /**
     * 业务线
     */
    private Byte accountType;

    public BigDecimal calculateUAmount() {
        return count.multiply(rate);
    }
}
