package com.upex.reconciliation.service.dao.entity;


import com.upex.mixcontract.common.repo.AbstractEntity;
import com.upex.mixcontract.common.repo.CombinedKeys;
import lombok.*;

import java.util.Date;


/**
 * 幂等表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbBillIdempotentRecord extends AbstractEntity {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 帐户id
     */
    private Long accountId;

    /**
     * 幂等健，由offset+业务id组成
     */
    private String uniKey;

    private Long timeSlice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据版本字段
     */
    private Long version;


    @Override
    public void setVersion(long version) {

    }

    @Override
    public CombinedKeys getUniqueKey() {
        return null;
    }
}