package com.upex.reconciliation.service.dao.bill.entity;

import lombok.*;

import java.util.Date;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @date 2020-05-11 11:13:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BillUser {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 最后一次检查成功时间
     */
    private Date checkOkTime;

    /**
     * 初始对账日期
     */
    private Date initialTime;

    /**
     * 当前检查状态
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

}
