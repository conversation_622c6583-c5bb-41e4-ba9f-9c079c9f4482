package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty;
import com.upex.reconciliation.service.model.dto.QueryDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("assetsBillCoinTypePropertyMapper")
public interface AssetsBillCoinTypePropertyMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(AssetsBillCoinTypeProperty record);

    AssetsBillCoinTypeProperty selectByPrimaryKey(Long id);

    int updateByPrimaryKey(AssetsBillCoinTypeProperty record);

    List<AssetsBillCoinTypeProperty> selectByCheckTime(@Param("checkTime") Date checkTime,
                                                       @Param("bizType") String bizType,
                                                       @Param("billCheckType") String billCheckType,
                                                       @Param("billCheckParam") String billCheckParam);


    List<AssetsBillCoinTypeProperty> selectByCoinIdCheckTime(@Param("checkTime") Date checkTime,
                                                             @Param("coinId") Integer coinId,
                                                             @Param("billCheckType") String billCheckType,
                                                             @Param("billCheckParam") String billCheckParam);

    boolean deleteByCheckTime(@Param("billCheckType") String billCheckType,
                              @Param("billCheckParam") String billCheckParam,
                              @Param("checkTime") Date checkTime,
                              @Param("batchSize") Long batchSize);


    /**
     * 根据币种、业务类型、checkTime查询记录
     *
     * @param coinId
     * @param bizType
     * @param checkTime
     * @param billCheckType
     * @param billCheckParam
     * @return
     */
    AssetsBillCoinTypeProperty selectByCoinIdTypeAndCheckTime(@Param("coinId") Integer coinId,
                                                              @Param("bizType") String bizType,
                                                              @Param("checkTime") Date checkTime,
                                                              @Param("billCheckType") String billCheckType,
                                                              @Param("billCheckParam") String billCheckParam);


    /**
     * 插入记录
     *
     * @param record
     * @param billCheckType
     * @param billCheckParam
     * @return
     */
    int insertRecord(@Param("record") AssetsBillCoinTypeProperty record,
                     @Param("billCheckType") String billCheckType,
                     @Param("billCheckParam") String billCheckParam);

    /**
     * 更新记录
     *
     * @param record
     * @param billCheckType
     * @param billCheckParam
     * @return
     */
    int updateRecord(@Param("record") AssetsBillCoinTypeProperty record,
                     @Param("billCheckType") String billCheckType,
                     @Param("billCheckParam") String billCheckParam);

    /**
     * 批量写入
     *
     * @param records
     * @param billCheckType
     * @param billCheckParam
     */
    int batchInsert(@Param("records") List<AssetsBillCoinTypeProperty> records,
                    @Param("billCheckType") String billCheckType,
                    @Param("billCheckParam") String billCheckParam);


    int batchUpdate(@Param("list") List<AssetsBillCoinTypeProperty> list,
                    @Param("billCheckType") String billCheckType,
                    @Param("billCheckParam") String billCheckParam);

    /**
     * 查询最后一次检查成功时间
     *
     * @param checkTime
     * @param billCheckType
     * @param billCheckParam
     * @return
     */
    Date selectLastCheckOkTime(@Param("checkTime") Date checkTime,
                               @Param("billCheckType") String billCheckType,
                               @Param("billCheckParam") String billCheckParam);

    /**
     * 删除重置对账时间之后的记录
     *
     * @param resetCheckTime
     * @param billCheckType
     * @param billCheckParam
     * @return
     */
    int deleteAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                          @Param("billCheckType") String billCheckType,
                          @Param("billCheckParam") String billCheckParam);

    /**
     * 删除历史记录
     *
     * @param deleteEndTime
     * @param billCheckType
     * @param billCheckParam
     * @return
     */
    int deleteHistoryRecord(@Param("deleteEndTime") Date deleteEndTime,
                            @Param("billCheckType") String billCheckType,
                            @Param("billCheckParam") String billCheckParam);

    /**
     * 获取最小的时间
     *
     * @param deleteEndTime 删除时间
     * @return java.util.Date
     * @throws
     * @Date 2022/7/11 4:47 PM
     * <AUTHOR>
     */
    Date selectMinDate(@Param("deleteEndTime") Date deleteEndTime,
                       @Param("accountType") Integer accountType,
                       @Param("accountParam") String accountParam);

    /**
     * 根据时间分页查询id
     *
     * @param queryDTO 查询dto
     * @return java.util.List<java.lang.Long>
     * @Date 2022/7/11 19:59 PM
     * <AUTHOR>
     */
    List<Long> pageListIdByTime(QueryDTO queryDTO);

    /**
     * 删除历史记录
     *
     * @param ids          主键集合
     * @param accountType  账户类型
     * @param accountParam 账户参数
     * @return
     * @Date 2022/7/11 7:42 PM
     */
    int deleteByIds(@Param("ids") List<Long> ids,
                    @Param("accountType") String accountType,
                    @Param("accountParam") String accountParam);

    /**
     * 获取最新的对账时间
     *
     * @param accountType  账户类型
     * @param accountParam 账户参数
     * @Date 2022/7/11 4:47 PM
     * <AUTHOR>
     */
    AssetsBillCoinTypeProperty selectTheLatestData(@Param("accountType") String accountType,
                                                   @Param("accountParam") String accountParam);

    /**
     * 查询时间范围内所有的checkTime
     *
     * @param queryDTO
     * @return
     */
    List<Date> selectCheckTimeList(QueryDTO queryDTO);

    /**
     * 查询指定时间checkTime id列表
     *
     * @param accountType
     * @param accountParam
     * @param startTime
     * @param deleteSyncPos
     * @param fixLength
     * @return
     */
    List<Long> selectRecordByCheckTime(
            @Param("accountType") String accountType,
            @Param("accountParam") String accountParam,
            @Param("startTime") Date startTime,
            @Param("deleteSyncPos") Long deleteSyncPos,
            @Param("fixLength") Integer fixLength);

    /**
     * 获取时间片id
     *
     * @param checkTime
     * @return
     */
    Long getIdByCheckTime(@Param("accountType") String accountType,
                          @Param("accountParam") String accountParam,
                          @Param("checkTime") Date checkTime,
                          @Param("operation") String operation);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param batchSize
     * @return
     */
    Boolean deleteByMaxId(@Param("accountType") String accountType,
                          @Param("accountParam") String accountParam,
                          @Param("maxId") Long maxId,
                          @Param("batchSize") Long batchSize);

    /**
     * 统计时间片记录数
     *
     * @param checkTime
     * @param bizType
     * @param billCheckType
     * @param billCheckParam
     * @return
     */
    Long countByCheckTime(@Param("checkTime") Date checkTime,
                          @Param("bizType") String bizType,
                          @Param("billCheckType") String billCheckType,
                          @Param("billCheckParam") String billCheckParam);

    List<AssetsBillCoinTypeProperty> selectPageByCheckTime(
            @Param("checkTime") Date checkTime,
            @Param("bizType") String bizType,
            @Param("billCheckType") String billCheckType,
            @Param("billCheckParam") String billCheckParam,
            @Param("startOffset") Long startOffset,
            @Param("pageSize") Integer pageSize);

    /**
     * 获取币种快照资产
     *
     * @param billCheckType
     * @param billCheckParam
     * @param snapshotTime
     * @param coinIdsList
     * @param bizTypeList
     * @return
     */
    List<AssetsBillCoinTypeProperty> getCoinTypeSnapshotAssets(
            @Param("billCheckType") String billCheckType,
            @Param("billCheckParam") String billCheckParam,
            @Param("snapshotTime") Date snapshotTime,
            @Param("coinIdsList") List<Integer> coinIdsList,
            @Param("bizTypeList") List<String> bizTypeList);

    /**
     * 删除指定时间之前的记录
     *
     * @param billCheckType
     * @param billCheckParam
     * @param checkTime
     * @param batchSize
     * @return
     */
    boolean deleteByLtCheckTime(@Param("billCheckType") String billCheckType,
                                 @Param("billCheckParam") String billCheckParam,
                                 @Param("checkTime") Date checkTime,
                                 @Param("batchSize") Long batchSize);
}