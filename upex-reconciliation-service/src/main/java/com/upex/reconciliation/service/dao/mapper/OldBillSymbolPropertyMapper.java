package com.upex.reconciliation.service.dao.mapper;


import com.upex.reconciliation.service.dao.entity.BillSymbolProperty;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/27 下午3:02
 * @Description
 */
@Repository("oldBillSymbolPropertyMapper")
public interface OldBillSymbolPropertyMapper {

    List<BillSymbolProperty> selectListByCheckTime(@Param("accountType") Byte accountType,
                                                   @Param("accountParam") String accountParam,
                                                   @Param("checkOkTime") Date checkOkTime);
}
