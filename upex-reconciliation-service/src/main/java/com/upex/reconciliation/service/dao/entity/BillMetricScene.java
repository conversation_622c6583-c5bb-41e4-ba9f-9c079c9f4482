package com.upex.reconciliation.service.dao.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-15
 */
public class BillMetricScene implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    private Byte status;

    /**
     * 场景名
     */
    private String name;

    /**
     * 备注
     */
    private String note;

    /**
     * crontab表达式，针对定时式，给出执行周期
     */
    private String crontabExpr;

    /**
     * 类型 1流式处理场景  2定时任务聚合式场景
     */
    private String type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据版本字段
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getCrontabExpr() {
        return crontabExpr;
    }

    public void setCrontabExpr(String crontabExpr) {
        this.crontabExpr = crontabExpr;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }
}