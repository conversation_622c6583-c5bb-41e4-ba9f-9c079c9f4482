package com.upex.reconciliation.service.model.alarm;

import com.upex.reconciliation.service.common.constants.AlarmTemplateLevelEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateQpsEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateTypeEnum;
import lombok.Data;

@Data
public class AlarmTemplateModel {
    private String name;
    private String code;
    private AlarmTemplateTypeEnum type;
    /***0 不限制***/
    private AlarmTemplateQpsEnum qpsEnum;
    /***0 不限制***/
    private AlarmTemplateLevelEnum level;
    private String text;
}
