package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillSymbolCoinProperty;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/27 下午3:02
 * @Description
 */
@Repository("billSymbolCoinPropertyMapper")
public interface BillSymbolCoinPropertyMapper {

    Boolean batchDelete(@Param("beginId") Long beginId, @Param("pageSize") Long pageSize, @Param("accountType") Byte accountType, @Param("accountParam") String accountParam);


    Boolean deleteByCheckTime(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("checkTime") Date checkTime);


    /**
     * 批量添加
     *
     * @param records
     * @param accountType
     * @param accountParam
     */
    int batchInsert(@Param("records") List<BillSymbolCoinProperty> records,
                    @Param("accountType") Byte accountType,
                    @Param("accountParam") String accountParam);

    /**
     * 删除相应数据
     *
     * @param accountType
     * @param accountParam
     */
    int deleteRecords(@Param("resetCheckTime") Date resetCheckTime,
                      @Param("accountType") Byte accountType,
                      @Param("accountParam") String accountParam);

    /**
     * 查询币种盈亏数据
     *
     * @param accountType  账户类型
     * @param accountParam 账户参数
     * @param snapshotTime 快照时间
     * @return {@link List< BillSymbolCoinProperty> }
     * <AUTHOR>
     * @date 2023/5/15 19:00
     */
    List<BillSymbolCoinProperty> selectBillSymbolCoinProperTyList(@Param("accountType") Byte accountType,
                                                                  @Param("accountParam") String accountParam,
                                                                  @Param("snapshotTime") Date snapshotTime);

    /**
     * 根据checkTime查询交易对+币种维度数据
     *
     * @param checkTime
     * @param accountType
     * @param accountParam
     * @return List<BillSymbolCoinProperty>
     */
    List<BillSymbolCoinProperty> queryByCheckTime(
            @Param("checkTime") Date checkTime,
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam);

    List<BillSymbolCoinProperty> selectListByCheckTime(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("checkOkTime") Date checkOkTime);

    /**
     * 获取时间片id
     *
     * @param checkTime
     * @return
     */
    Long getIdByCheckTime(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("checkTime") Date checkTime,
                          @Param("operation") String operation);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param batchSize
     * @return
     */
    Boolean deleteByMaxId(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("maxId") Long maxId,
                          @Param("batchSize") Long batchSize);

    /**
     * 删除数据
     *
     * @param id
     * @return
     */
    int deleteById(@Param("accountType") Byte accountType,
                   @Param("accountParam") String accountParam,
                   @Param("id") Long id);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param batchSize
     * @return
     */
    boolean deleteByLtCheckTime(@Param("accountType") Byte accountType,
                                @Param("accountParam") String accountParam,
                                @Param("checkTime") Date checkTime,
                                @Param("batchSize") Long batchSize);

    /**
     * 更新数据
     *
     * @param accountType
     * @param accountParam
     * @param record
     * @return
     */
    Boolean updateById(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("record") BillSymbolCoinProperty record);
}
