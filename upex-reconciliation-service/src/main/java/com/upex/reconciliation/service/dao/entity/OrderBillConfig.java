package com.upex.reconciliation.service.dao.entity;

import lombok.*;

import java.util.Date;

import static com.upex.reconciliation.service.common.constants.BillConstants.SEPARATOR;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderBillConfig {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 账户类型
     */
    private String orderType;
    /**
     * 账户类型
     */
    private String orderParam;
    /**
     * 对账成功时间
     */
    private Date checkOkTime;
    /**
     * 当前检查状态
     */
    private Byte status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 获取检查唯一key
     *
     * @return
     */
    public String getCheckUniqKey() {
        return this.orderType + SEPARATOR + this.orderParam;
    }
}
