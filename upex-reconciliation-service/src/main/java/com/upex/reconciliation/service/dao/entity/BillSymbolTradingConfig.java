package com.upex.reconciliation.service.dao.entity;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 盈亏对账配置表
 *
 * <AUTHOR>
 * @date 2022/12/8 17:51
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BillSymbolTradingConfig {
    /***主键id***/
    private Long id;
    /***账户类型***/
    private Byte accountType;
    /***账户类型***/
    private String accountParam;
    /***标记交易对***/
    private String symbolId;
    /***初始值***/
    private BigDecimal initValue = BigDecimal.ZERO;
    /***对账成功时间***/
    private Date checkOkTime;
    /***创建时间***/
    private Date createTime = new Date();
    /***最后更新时间***/
    private Date updateTime = new Date();
}