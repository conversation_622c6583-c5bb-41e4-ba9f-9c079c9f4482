package com.upex.reconciliation.service.model.config;

import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务收入对账配置
 */
@Data
public class CommissionCheckAccountTypeConfig {


    private String name;

    /**
     * 优先按单条业务线走
     */
    private Byte accountType;

    private String accountParam;

    /**
     * 手续费类型
     */
    private List<String> commissionBizTypes;


}
