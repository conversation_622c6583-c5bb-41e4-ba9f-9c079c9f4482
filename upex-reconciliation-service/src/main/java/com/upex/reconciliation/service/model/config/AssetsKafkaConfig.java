package com.upex.reconciliation.service.model.config;

import lombok.Data;

import java.util.Map;

/**
 * 总账kafka配置
 */
@Data
public class AssetsKafkaConfig {
    /***总账消费者配置***/
    private Map<String, AssetsKafkaConsumerConfig> kafkaConsumerConfig;
    /***时间片数据超过xxx限流***/
    private Integer timesliceSizeRateLimit = 60;

    /**
     * 获取kafka配置
     *
     * @param accountType
     * @return
     */
    public AssetsKafkaConsumerConfig getKafkaConsumerConfig(String accountType) {
        return kafkaConsumerConfig.get(accountType);
    }
}
