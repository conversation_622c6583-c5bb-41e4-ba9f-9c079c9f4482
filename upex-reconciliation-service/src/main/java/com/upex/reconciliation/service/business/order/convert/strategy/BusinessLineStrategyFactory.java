package com.upex.reconciliation.service.business.order.convert.strategy;

import com.upex.reconciliation.service.business.convert.enums.BusinessLineEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务线策略工厂
 * 负责管理和获取不同业务线的处理策略
 */
@Slf4j
@Component
public class BusinessLineStrategyFactory {

    @Autowired
    private List<BusinessLineStrategy> strategies;

    private final Map<BusinessLineEnum, BusinessLineStrategy> strategyMap = new HashMap<>();
    private final Map<Integer, BusinessLineStrategy> accountTypeStrategyMap = new HashMap<>();

    @PostConstruct
    public void initializeStrategies() {
        if (strategies != null) {
            for (BusinessLineStrategy strategy : strategies) {
                BusinessLineEnum strategyName = strategy.getStrategyName();
                strategyMap.put(strategyName, strategy);
            }
        }
        
        log.info("Initialized {} business line strategies", strategyMap.size());
    }

    /**
     * 根据业务线获取策略
     *
     * @param businessLine 业务线枚举
     * @return 对应的策略，如果没有找到则返回null
     */
    public BusinessLineStrategy getStrategy(BusinessLineEnum businessLine) {
        BusinessLineStrategy strategy = strategyMap.get(businessLine);
        if (strategy == null) {
            log.warn("No strategy found for business line: {}", businessLine);
        }
        return strategy;
    }

//    /**
//     * 根据account_type获取策略
//     *
//     * @param accountType account_type
//     * @return 对应的策略，如果没有找到则返回null
//     */
//    public BusinessLineStrategy getStrategyByAccountType(Integer accountType) {
//        BusinessLineStrategy strategy = accountTypeStrategyMap.get(accountType);
//        if (strategy == null) {
//            log.warn("No strategy found for account type: {}", accountType);
//        }
//        return strategy;
//    }
//
//
//    /**
//     * 获取所有支持的业务线
//     *
//     * @return 支持的业务线列表
//     */
//    public List<BusinessLineEnum> getSupportedBusinessLines() {
//        return List.copyOf(strategyMap.keySet());
//    }
//
//    /**
//     * 获取所有支持的account_type
//     *
//     * @return 支持的account_type列表
//     */
//    public List<Integer> getSupportedAccountTypes() {
//        return List.copyOf(accountTypeStrategyMap.keySet());
//    }
//
//    /**
//     * 获取策略统计信息
//     *
//     * @return 策略统计信息
//     */
//    public Map<String, Object> getStrategyStatistics() {
//        Map<String, Object> stats = new HashMap<>();
//        stats.put("totalStrategies", strategyMap.size());
//        stats.put("supportedBusinessLines", getSupportedBusinessLines());
//        stats.put("supportedAccountTypes", getSupportedAccountTypes());
//
//        Map<String, String> strategyDetails = new HashMap<>();
//        for (Map.Entry<BusinessLineEnum, BusinessLineStrategy> entry : strategyMap.entrySet()) {
//            strategyDetails.put(entry.getKey().getCode(), entry.getValue().getStrategyName());
//        }
//        stats.put("strategyDetails", strategyDetails);
//
//        return stats;
//    }
//
//    /**
//     * 刷新策略映射（用于动态更新）
//     */
//    public void refreshStrategies() {
//        log.info("Refreshing business line strategies");
//        strategyMap.clear();
//        accountTypeStrategyMap.clear();
//        initializeStrategies();
//    }
//
//    /**
//     * 验证策略配置
//     *
//     * @return 验证结果
//     */
//    public boolean validateStrategies() {
//        boolean isValid = true;
//
//        // 检查是否所有业务线都有对应的策略
//        for (BusinessLineEnum businessLine : BusinessLineEnum.values()) {
//            if (!strategyMap.containsKey(businessLine)) {
//                log.warn("Missing strategy for business line: {}", businessLine);
//                isValid = false;
//            }
//        }
//
//        // 检查是否有重复的account_type映射
//        Map<Integer, Integer> accountTypeCount = new HashMap<>();
//        for (Integer accountType : accountTypeStrategyMap.keySet()) {
//            accountTypeCount.put(accountType, accountTypeCount.getOrDefault(accountType, 0) + 1);
//        }
//
//        for (Map.Entry<Integer, Integer> entry : accountTypeCount.entrySet()) {
//            if (entry.getValue() > 1) {
//                log.warn("Duplicate account type mapping found: {} mapped to {} strategies",
//                        entry.getKey(), entry.getValue());
//                isValid = false;
//            }
//        }
//
//        if (isValid) {
//            log.info("All business line strategies are valid");
//        } else {
//            log.warn("Some business line strategies have validation issues");
//        }
//
//        return isValid;
//    }
}
