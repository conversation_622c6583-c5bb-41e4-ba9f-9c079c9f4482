package com.upex.reconciliation.service.common.context;

import com.upex.reconciliation.service.business.*;
import com.upex.reconciliation.service.business.module.processor.MonitorCheckProcessor;
import com.upex.reconciliation.service.business.order.convert.match.OrderDataProcessingService;
import com.upex.reconciliation.service.business.ruleengine.core.RuleEngine;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.RateLimiterManager;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.CapitalOrderService;
import com.upex.utils.task.TaskManager;
import com.upex.utils.util.SerialNoGenerator;
import lombok.Data;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 业务上下文类
 * 用于传递所有的 @service对象到不同的 Command 、Module
 */
@Service
@Data
public class ReconciliationSpringContext {
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private RateLimiterManager rateLimiterManager;
    @Resource
    private CommonService commonService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource(name = "reconRedisTemplate")
    private RedisTemplate<String, Object> reconRedisTemplate;
    @Resource
    private MonitorCheckProcessor monitorCheckProcessor;
    @Resource
    private BillUserService billUserService;
    @Resource
    private AssetsBillConfigService assetsBillConfigService;
    @Resource
    private AssetsBillCoinPropertyService assetsBillCoinPropertyService;
    @Resource
    private AssetsBillCoinTypePropertyService assetsBillCoinTypePropertyService;
    @Resource
    private MemoryDataService memoryDataService;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;
    @Resource
    private BillDbHelper dbHelper;
    @Resource
    private AssetsBillConfigSnapshotService assetsBillConfigSnapshotService;
    @Resource
    private BillAllConfigService billAllConfigService;
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;
    @Resource
    private RuleEngine ruleEngine;

    @Resource
    private CapitalOrderService capitalOrderService;
    @Resource
    private TaskManager taskManager;
    @Resource
    private TaskManager asyncLoadUserTaskManager;
    @Resource
    private ReconSystemAccountService reconSystemAccountService;
    @Resource(name = "kafkaProducer")
    private KafkaProducer<String, String> kafkaProducer;
    @Resource
    private ReconInnerTransferService reconInnerTransferService;
    @Resource
    private FinancialBillOrderService financialBillOrderService;
    @Resource
    private BillDelayAccountService billDelayAccountService;
    @Resource
    private AssetsContractProfitCoinDetailService assetsContractProfitCoinDetailService;
    @Resource
    private SerialNoGenerator serialNoGenerator;
    @Resource
    private AssetsContractProfitSymbolDetailService assetsContractProfitSymbolDetailService;
    @Resource
    private BillUserWithdrawProfitRecordService billUserWithdrawProfitRecordService;
    @Resource
    private TaskManager userProfitRtTaskManager;
    @Resource
    private TaskManager userBeginAssetsTaskManager;
    @Resource
    protected UserBeginAssetsRedisService userBeginAssetsRedisService;
    @Resource
    private OrderDataProcessingService orderDataProcessingService;
    @Resource
    private OrderBillConfigService orderBillConfigService;
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;
}
