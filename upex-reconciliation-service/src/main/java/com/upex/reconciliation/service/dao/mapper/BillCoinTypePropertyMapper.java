package com.upex.reconciliation.service.dao.mapper;


import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Repository("billCoinTypePropertyMapper")
public interface BillCoinTypePropertyMapper {

    Boolean batchDelete(@Param("beginId") Long beginId, @Param("pageSize") Long pageSize, @Param("accountType") Byte accountType, @Param("accountParam") String accountParam);

    Boolean updateById(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("data") BillCoinTypeProperty data);

    BillCoinTypeProperty selectById(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("id") Long id);

    int batchInsert(@Param("records") List<BillCoinTypeProperty> records,
                    @Param("accountType") Byte accountType,
                    @Param("accountParam") String accountParam);

    Boolean deleteByCheckTime(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("checkTime") Date checkTime);

    /**
     * 查询某一段时间内的账单
     *
     * @param startTime
     * @param endTime
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillCoinTypeProperty> listBillBetweenTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                                   @Param("accountType") Byte accountType,
                                                   @Param("accountParam") String accountParam);

    /**
     * 删除重置对账时间之后的记录
     *
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int deleteAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                          @Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam);

    List<BillCoinTypeProperty> selectCheckTimeRecordPage(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime,
            @Param("minId") Long minId,
            @Param("pageSize") Integer pageSize
    );


    /**
     * 查询截止到某一时间点的资产
     *
     * @param endTime
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillCoinTypeProperty> selectAssetsByEndTime(@Param("endTime") Date endTime,
                                                     @Param("accountType") Byte accountType,
                                                     @Param("accountParam") String accountParam);


    List<BillCoinTypeProperty> selectByCoinIdCheckTimeNotInBizTypes(@Param("accountType") Integer accountType,
                                                                    @Param("accountParam") String accountParam,
                                                                    @Param("coinId") Integer coinId,
                                                                    @Param("checkTime") Date checkTime,
                                                                    @Param("notInBizTypes") List<String> notInBizTypes);


    List<BillCoinTypeProperty> selectByCoinIdCheckTimeInBizTypes(@Param("accountType") Integer accountType,
                                                                    @Param("accountParam") String accountParam,
                                                                    @Param("coinId") Integer coinId,
                                                                    @Param("checkTime") Date checkTime,
                                                                    @Param("inBizTypes") List<String> inBizTypes);

    List<BillCoinTypeProperty> selectAllCoinByCheckTimeInBizTypes(@Param("accountType") Integer accountType,
                                                                  @Param("accountParam") String accountParam,
                                                                  @Param("checkTime") Date checkTime,
                                                                  @Param("inBizTypes") List<String> inBizTypes);


    List<BillCoinTypeProperty> selectRangeCheckTimeRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime);

    List<BillCoinTypeProperty> selectByCoinIdCheckTime(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("coinId") Integer coinId,
            @Param("checkTime") Date checkTime);


    List<BillCoinTypeProperty> selectBizTypeCheckTimeRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime,
            @Param("bizType") String bizType
    );

    List<BillCoinTypeProperty> selectBizTypeCoinId(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime,
            @Param("coinId") Integer coinId,
            @Param("bizType") String bizType
    );


    /**
     * 获取时间片id
     *
     * @param checkTime
     * @return
     */
    Long getIdByCheckTime(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("checkTime") Date checkTime,
                          @Param("operation") String operation);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param batchSize
     * @return
     */
    Boolean deleteByMaxId(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("maxId") Long maxId,
                          @Param("batchSize") Long batchSize);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param batchSize
     * @return
     */
    boolean deleteByLtCheckTime(@Param("accountType") Byte accountType,
                                @Param("accountParam") String accountParam,
                                @Param("checkTime") Date checkTime,
                                @Param("batchSize") Long batchSize);
}
