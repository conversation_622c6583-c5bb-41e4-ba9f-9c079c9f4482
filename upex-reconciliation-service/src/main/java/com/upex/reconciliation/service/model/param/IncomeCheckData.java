package com.upex.reconciliation.service.model.param;

import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/16 17:23
 */
@Data
public class IncomeCheckData {


    private Integer coinId;

    private String coinName;

    private String token;
    private BigDecimal incomeCountChange;

    private BigDecimal feeCountChange;

    private BigDecimal diffCount;

    private BigDecimal diffAmountUsdt;

    private BigDecimal tolerance = BigDecimal.ZERO;

    private Map<String, BigDecimal> bizTypeChangeMap = new HashMap<>();


    public boolean checkIncome() {
        return incomeCountChange.subtract(feeCountChange).abs().compareTo(tolerance) <= 0;
    }

}
