package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinTypeProperty;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("oldAssetsBillCoinTypePropertyMapper")
public interface OldAssetsBillCoinTypePropertyMapper {


    List<AssetsBillCoinTypeProperty> selectByCheckTime(@Param("checkTime") Date checkTime,
                                                       @Param("bizType") String bizType,
                                                       @Param("billCheckType") String billCheckType,
                                                       @Param("billCheckParam") String billCheckParam);


    List<AssetsBillCoinTypeProperty> selectByCoinIdCheckTime(@Param("checkTime") Date checkTime,
                                                       @Param("coinId") Integer coinId,
                                                       @Param("billCheckType") String billCheckType,
                                                       @Param("billCheckParam") String billCheckParam);


}