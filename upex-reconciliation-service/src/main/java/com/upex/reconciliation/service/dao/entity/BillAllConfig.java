package com.upex.reconciliation.service.dao.entity;

import com.upex.mixcontract.common.repo.AbstractEntity;
import com.upex.mixcontract.common.repo.CombinedKeys;
import com.upex.reconciliation.service.common.constants.BillConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BillAllConfig extends AbstractEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账户类型
     */
    private Byte accountType;

    /**
     * 账户类型
     */
    private String accountParam;

    /**
     * 用户同步位置
     */
    private Long syncPos;

    /**
     * 对账成功时间
     */
    private Date checkOkTime;

    /**
     * 是否修改对账时间
     */
    private Boolean flag;

    /**
     * 当前检查状态
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 获取唯一key
     *
     * @return
     */
    public String getUniqKey() {
        return this.accountType.toString() + BillConstants.SEPARATOR + this.accountParam;
    }

    @Override
    public Long getVersion() {
        return null;
    }

    @Override
    public void setVersion(long version) {

    }

    @Override
    public CombinedKeys getUniqueKey() {
        return null;
    }
}
