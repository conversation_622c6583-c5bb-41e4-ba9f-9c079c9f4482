package com.upex.reconciliation.service.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FixDataRecord  {

    private Long id;
    private Long batchId;
    private String tableNamePrefix;
    private Integer accountType;
    private String accountParam;
    private String beforeData;
    private String afterData;
    private Date createTime;
    private Date updateTime;

}
