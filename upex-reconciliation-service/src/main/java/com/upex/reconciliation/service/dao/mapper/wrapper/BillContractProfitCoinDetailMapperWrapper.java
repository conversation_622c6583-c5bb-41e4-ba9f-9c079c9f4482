package com.upex.reconciliation.service.dao.mapper.wrapper;

import com.upex.reconciliation.service.business.createtablebyroute.BillContractProfitTransferTableCreator;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitCoinDetailMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component("billContractProfitCoinDetailMapperWrapper")
public class BillContractProfitCoinDetailMapperWrapper implements BillContractProfitCoinDetailMapper {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billContractProfitCoinDetailMapper")
    private BillContractProfitCoinDetailMapper billContractProfitCoinDetailMapper;

    @Override
    public List<BillContractProfitCoinDetail> getBillContractProfitCoinDetailList(Byte accountType, String accountParam, Date checkOkTime, String profitType) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.getBillContractProfitCoinDetailList(accountType, accountParam, checkOkTime, profitType));
    }

    @Override
    public List<BillContractProfitCoinDetail> getAllBillContractProfitCoinDetailList(Byte accountType, String accountParam, List<String> profitTypeList, Date checkOkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.getAllBillContractProfitCoinDetailList(accountType, accountParam, profitTypeList, checkOkTime));
    }

    @Override
    public int batchInsert(Byte accountType, String accountParam, List<BillContractProfitCoinDetail> list) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.batchInsert(accountType, accountParam, list));
    }

    @Override
    public int batchInsertHis(Byte accountType, String accountParam, List<BillContractProfitCoinDetail> list) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.batchInsertHis(accountType, accountParam, list));
    }

    @Override
    public int batchUpdateStatus(Byte accountType, String accountParam, int newStatus, int oldStatus, Date updateTime, List<Long> ids) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.batchUpdateStatus(accountType, accountParam, newStatus, oldStatus, updateTime, ids));
    }

    @Override
    public int deleteAfterRecord(Date resetCheckTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.deleteAfterRecord(resetCheckTime, accountType, accountParam));
    }

    @Override
    public List<BillContractProfitCoinDetail> getAllAfterRecord(Date resetCheckTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.getAllAfterRecord(resetCheckTime, accountType, accountParam));
    }

    @Override
    public Date getLastCheckOkTime(Byte accountType, String accountParam, String profitType) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.getLastCheckOkTime(accountType, accountParam, profitType));
    }

    @Override
    public int updateById(Byte accountType, String accountParam, BillContractProfitCoinDetail record) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.updateById(accountType, accountParam, record));
    }

    @Override
    public int deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }

    @Override
    public Boolean batchDelete(Long minId, Long deleteSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.batchDelete(minId, deleteSize, accountType, accountParam));
    }

    @Override
    public List<BillContractProfitCoinDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billContractProfitCoinDetailMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime));
    }
}
