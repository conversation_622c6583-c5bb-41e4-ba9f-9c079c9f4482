package com.upex.reconciliation.service.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaxMinIdDTO {
    private Long maxId;
    private Long minId;

    /**
     * 根据id分段
     *
     * @param batchSize
     * @return
     */
    public List<Long[]> buildIdSegments(Long batchSize) {
        if (maxId == null || minId == null || batchSize == null) {
            return new ArrayList<>();
        }
        List<Long[]> idSegmentList = new ArrayList<>();
        for (int i = 0; i < ((maxId - minId) / batchSize + 1); i++) {
            idSegmentList.add(new Long[]{minId + i * batchSize, minId + (i + 1) * batchSize});
        }
        return idSegmentList;
    }
}