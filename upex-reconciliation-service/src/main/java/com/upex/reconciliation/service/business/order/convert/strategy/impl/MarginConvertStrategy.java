package com.upex.reconciliation.service.business.order.convert.strategy.impl;

import com.upex.reconciliation.service.business.convert.ReconOrderResult;
import com.upex.reconciliation.service.business.convert.config.BusinessLineConfig;
import com.upex.reconciliation.service.business.convert.enums.BusinessLineEnum;
import com.upex.reconciliation.service.business.convert.model.CombinedOrderData;
import com.upex.reconciliation.service.business.order.convert.strategy.AbstractConvertBusinessLineStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 杠杆现货业务线处理策略
 */
@Component
@Slf4j
public class MarginConvertStrategy extends AbstractConvertBusinessLineStrategy {


    @Override
    public ReconOrderResult validatePriceData(CombinedOrderData combinedOrderData, BusinessLineConfig config) {
        return ReconOrderResult.success();
    }

    @Override
    protected BusinessLineEnum getSupportedBusinessLine() {

        return BusinessLineEnum.CONVERT_MARGIN;
    }
}
