package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillUserPosition;
import com.upex.reconciliation.service.dao.entity.BillUserWithdrawProfitRecord;
import com.upex.reconciliation.service.dao.entity.ReconWithdrawCheckRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("billUserWithdrawProfitRecordMapper")
public interface BillUserWithdrawProfitRecordMapper {


    int batchInsert(@Param("records") List<BillUserWithdrawProfitRecord> records);

    int insert(@Param("record") BillUserWithdrawProfitRecord record);

    List<Long> getUserIdsIdRange(@Param("startId") Long startId,@Param("endId") Long endId);

    Long selectMaxId(@Param("startDate") Date startDate,@Param("endDate") Date endDate );

    Long selectMinId(@Param("startDate") Date startDate,@Param("endDate") Date endDate );
}
