package com.upex.reconciliation.service.dao.entity;

import lombok.Data;

import java.util.Date;

/**
 * 对账白名单配置表
 *
 * <AUTHOR>
 * @Date 2025/2/5
 */
@Data
public class BillWhiteListConfig {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 白名单
     */
    private String whiteList;

    /**
     * 状态(0:无效，1:有效)
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
