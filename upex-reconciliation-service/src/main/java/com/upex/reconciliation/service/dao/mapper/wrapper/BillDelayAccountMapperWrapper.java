package com.upex.reconciliation.service.dao.mapper.wrapper;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillDelayAccount;
import com.upex.reconciliation.service.dao.mapper.BillDelayAccountMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 延迟入账数据
 *
 * <AUTHOR>
 */
@Component("billDelayAccountMapperWrapper")
public class BillDelayAccountMapperWrapper implements BillDelayAccountMapper {
    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "billDelayAccountMapper")
    private BillDelayAccountMapper billDelayAccountMapper;

    @Override
    public int batchInsert(List<BillDelayAccount> list) {
        return dbHelper.doDbOpInReconMaster(() -> billDelayAccountMapper.batchInsert(list));
    }

    @Override
    public List<BillDelayAccount> selectDelayAccountExistsList(List<BillDelayAccount> list) {
        return dbHelper.doDbOpInReconMaster(() -> billDelayAccountMapper.selectDelayAccountExistsList(list));
    }

    @Override
    public List<BillDelayAccount> selectHistoryDelayAccountList(Long userId, Integer activeFlag) {
        return dbHelper.doDbOpInReconMaster(() -> billDelayAccountMapper.selectHistoryDelayAccountList(userId, activeFlag));
    }

    @Override
    public int updateDelayAccount(BillDelayAccount billDelayAccount) {
        return dbHelper.doDbOpInReconMaster(() -> billDelayAccountMapper.updateDelayAccount(billDelayAccount));
    }

    @Override
    public int updateDelayAccountByTime(Date checkOkDate, Integer activeFlag, Date updateTime) {
        return dbHelper.doDbOpInReconMaster(() -> billDelayAccountMapper.updateDelayAccountByTime(checkOkDate, activeFlag, updateTime));
    }

    @Override
    public boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billDelayAccountMapper.deleteByLtCheckTime(accountType, accountParam, checkTime, batchSize));
    }
}
