package com.upex.reconciliation.service.dao.entity;

import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.utils.NumberUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/7/27 下午2:44
 * @Description 混合合约(交易对)
 */
@Slf4j
@Data
public class BillSymbolProperty extends AbstractProperty {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 交易对标识id
     */
    private String symbolId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 检查时间
     */
    private Date checkTime;

    /**
     * 初始日期
     */
    private Date initialTime;
    /***是否存在负值仓位 不需要持久化 过程计算字段***/
    private Boolean existNegativePosition = false;

    @Override
    public String toString() {
        final StringBuilder stringBuilder = new StringBuilder("BillSymbolProperty{");
        stringBuilder.append(" id=").append(id);
        stringBuilder.append(", symbolId=").append(symbolId);
        stringBuilder.append(", changeProp1=").append(getChangeProp1());
        stringBuilder.append(", prop1=").append(getProp1());
        stringBuilder.append(", changeProp2=").append(getChangeProp2());
        stringBuilder.append(", prop2=").append(getProp2());
        stringBuilder.append(", changeProp3=").append(getChangeProp3());
        stringBuilder.append(", prop3=").append(getProp3());
        stringBuilder.append(", changeProp4=").append(getChangeProp4());
        stringBuilder.append(", prop4=").append(getProp4());
        stringBuilder.append(", changeProp5=").append(getChangeProp5());
        stringBuilder.append(", prop5=").append(getProp5());
        stringBuilder.append(", updateFlag=").append(getUpdateFlag());
        stringBuilder.append(", createTime=").append(createTime);
        stringBuilder.append(", updateTime=").append(updateTime);
        stringBuilder.append(", checkTime=").append(checkTime);
        stringBuilder.append(", initialTime=").append(initialTime);
        stringBuilder.append(", params='").append(getParams()).append('\'');
        stringBuilder.append('}');
        return stringBuilder.toString();
    }

    /**
     * list转map symbolId，BillSymbolProperty
     * 仅用于两个时间片数据合并和数据rebootlist拆分为map
     *
     * @param list
     * @return
     */
    public static Map<String, BillSymbolProperty> listToMapSumSymbol(List<BillSymbolProperty> list, boolean needBefore) {

        Map<String, BillSymbolProperty> symbolPropertyMap = new ConcurrentHashMap<>();
        // 数据转换
        list.stream().collect(Collectors.groupingBy(BillSymbolProperty::getSymbolId)).forEach((symbolId, symbolPropertyList) -> {
            if (symbolPropertyList.size() > 2) {
                throw new RuntimeException("非法数据结构");
            }
            BillSymbolProperty beforeBillSymbolProperty = symbolPropertyList.get(0);
            if (symbolPropertyList.size() == 2) {
                // 把增量数据和上期数据进行合并
                BillSymbolProperty currentBillSymbolProperty = symbolPropertyList.get(1);
                beforeBillSymbolProperty.setProp5(NumberUtil.add(beforeBillSymbolProperty.getProp5(), currentBillSymbolProperty.getChangeProp5()));
                if (needBefore) {
                    beforeBillSymbolProperty.setChangeProp5(beforeBillSymbolProperty.getChangeProp5().add(currentBillSymbolProperty.getChangeProp5()));
                } else {
                    beforeBillSymbolProperty.setChangeProp5(currentBillSymbolProperty.getChangeProp5());
                }
            }
            symbolPropertyMap.put(beforeBillSymbolProperty.getSymbolId(), beforeBillSymbolProperty);
        });
        return symbolPropertyMap;
    }

}
