package com.upex.reconciliation.service.business.order.convert.match;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.convert.ReconOrderResult;
import com.upex.reconciliation.service.business.convert.config.BusinessLineConfig;
import com.upex.reconciliation.service.business.convert.config.QuotedTypeConfig;
import com.upex.reconciliation.service.business.convert.enums.BusinessLineEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderStausEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.*;
import com.upex.reconciliation.service.business.order.convert.strategy.BusinessLineStrategy;
import com.upex.reconciliation.service.business.order.convert.strategy.BusinessLineStrategyFactory;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.service.ReconOrderFailureRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.upex.reconciliation.service.business.convert.ReconOrderConstants.*;

/**
 * 闪兑订单处理器
 * 处理闪兑相关的订单对账逻辑
 */
@Slf4j
@Component
public class ConvertOrderProcessor implements OrderProcessor {

    @Resource
    private BusinessLineStrategyFactory businessLineStrategyFactory;
    // todo 添加本地缓存减少Redis查询
    private final ConcurrentHashMap<String, Object> orderIdMappingCache = new ConcurrentHashMap<>();
    private static final DateTimeFormatter SHARD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    // 在类定义中添加锁映射
    private final ConcurrentHashMap<String, Lock> orderLocks = new ConcurrentHashMap<>();
    @Resource
    private ReconOrderFailureRecordService reconOrderFailureRecordService;

    @Resource(name = "reconRedisTemplate")
    private RedisTemplate<String, Object> reconRedisTemplate;

    @Resource
    private AlarmNotifyService alarmNotifyService;


    @Override
    public ReconOrderTypeEnum getSupportedOrderType() {
        return ReconOrderTypeEnum.CONVERT;
    }

    @Override
    public <T> void processOrder(T orderData, ReconOrderConfig config) {

        String orderId = "";
        if (orderData instanceof ConvertOrder) {
            ConvertOrder convertOrder = (ConvertOrder) orderData;
            BusinessLineConfig businessLineConfig = config.getBusinessLineByAccountType(convertOrder.getAccountType());
            QuotedTypeConfig quotedTypeConfig = businessLineConfig.getQuotedTypeConfig(convertOrder.getQuotedType());
            if (convertOrder.getBizOrderId() == null || !quotedTypeConfig.isEnabled()) {
                log.warn("Invalid convert order data: {}", convertOrder);
                return;
            }
            orderId = String.valueOf(convertOrder.getBizOrderId());
            String key = MAIN_ORDER_PREFIX + orderId;
            String value = JSON.toJSONString(convertOrder);
            // 使用业务线配置的过期时间
            int expireTime = config.getOrderExpireTime();
            reconRedisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.MINUTES);
            // 保存订单ID到索引
            saveMainOrderIndex(orderId, config);
        }
        // 操盘后划转单
        if (orderData instanceof TransOrder) {
            TransOrder transOrder = (TransOrder) orderData;
            orderId = String.valueOf(transOrder.getId());
            String key = TRANS_ORDER_PREFIX + orderId;
            String value = JSON.toJSONString(transOrder);
            // 使用业务线配置的过期时间
            int expireTime = config.getOrderExpireTime();
            reconRedisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.MINUTES);
        }
        // 保存操盘单
        if (orderData instanceof SpotHedgingOrder) {
            SpotHedgingOrder spotHedgingOrder = (SpotHedgingOrder) orderData;
            orderId = String.valueOf(spotHedgingOrder.getId());
            String key = SPOT_HEDGING_ORDER_PREFIX + orderId;
            String value = JSON.toJSONString(spotHedgingOrder);
            // 使用业务线配置的过期时间
            int expireTime = config.getOrderExpireTime();
            reconRedisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.MINUTES);
        }
        tryMatchOrderFlow(orderId, config);
        log.info("Saved main order data: {} ", orderData);


    }


    @Override
    public <T> void processFlow(T bill, ReconOrderConfig config) {

        FlowOrder flowOrder = (FlowOrder) bill;
        if (bill == null || flowOrder.getOrderId() == null) {
            log.warn("Invalid convert flow order data: {}", bill);
            return;
        }
        String bizType = flowOrder.getBizType();

        // 统一的ID转换逻辑
        String finalOrderId = convertToMainOrderId(flowOrder, bizType, config);
        // 如果返回null，说明已加入延迟队列，不继续处理
        if (finalOrderId == null) {
            log.info("Flow order added to delay queue, skipping immediate processing. BizType: {}, OrderId: {}",
                    bizType, flowOrder.getOrderId());
            return;
        }
        saveFlowOrderToRedis(flowOrder, config, finalOrderId);
        tryMatchOrderFlow(finalOrderId, config);
    }



    /**
     * 统一的订单ID转换逻辑
     * 将所有类型的流水ID转换为主订单ID
     */
    private String convertToMainOrderId(FlowOrder flowOrder, String bizType, ReconOrderConfig config) {

        String originalOrderId = flowOrder.getOrderId().toString();
        try {
            // 判断是否需要转换订单id的流水
            if (config.getNeedConvertBizTypes().contains(bizType)) {
                return handleFlowConversion(flowOrder, config, originalOrderId);
            } else {
                // 其他流水直接使用原始ID
                return originalOrderId;
            }
        } catch (Exception e) {
            log.error("Failed to convert orderId for bizType: {}, originalId: {}", bizType, originalOrderId, e);
            return originalOrderId;
        }
    }

    /**
     * 处理流水转换逻辑
     *
     * @param flowOrder
     * @param config
     * @param originalOrderId
     * @return
     */
    private String handleFlowConversion(FlowOrder flowOrder, ReconOrderConfig config, String originalOrderId) {

        // 1. 首先检查是否可能是直接匹配（OSl闪兑的原始ID就是主订单ID）
        // 检查主订单是否存在
        if (isMainOrderExists(originalOrderId)) {
            log.info("Margin flow orderId {} matches main order directly", originalOrderId);
            return originalOrderId;
        }
        // 2. 尝试通过转换链路获取主订单ID
        String convertedOrderId = convertFlowToMainOrderId(originalOrderId);
        if (!convertedOrderId.equals(originalOrderId)) {
            // 转换成功
            log.info("Successfully converted margin flow orderId from {} to {}", originalOrderId, convertedOrderId);
            return convertedOrderId;
        }
        // 3. 转换失败，加入延迟队列重试
        log.info("Margin flow conversion failed, adding to retry queue for orderId: {}", originalOrderId);
        addToDelayedQueue(flowOrder, config, originalOrderId, 0);
        return null; // 返回null表示已加入延迟队列
    }


    /**
     * 添加到延迟队列
     */
    private void addToDelayedQueue(FlowOrder flowOrder, ReconOrderConfig config, String originalOrderId, int retryCount) {

        long delayMs = calculateRetryDelay(retryCount,config);
        DelayedFlowOrder delayedOrder = new DelayedFlowOrder(
                flowOrder, config, delayMs, retryCount + 1, originalOrderId);
        delayedFlowQueue.offer(delayedOrder);
        log.info("Added margin flow to delay queue, orderId: {}, retryCount: {}, delayMs: {}",
                originalOrderId, retryCount + 1, delayMs);
    }



    /**
     * 尝试匹配订单流水
     */
    @Override
    public void tryMatchOrderFlow(String orderId, ReconOrderConfig reconOrderConfig) {

        try {
            // 获取主订单数据
            String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
            Object order = reconRedisTemplate.opsForValue().get(mainOrderKey);
            if (Objects.isNull(order) || StringUtils.isEmpty(order.toString())) {
                log.info("Main order not found for matching: {}", orderId);
                return;
            }
            ConvertOrder mainOrderData = JSON.parseObject(order.toString(), ConvertOrder.class);
            BusinessLineConfig businessLineConfig = reconOrderConfig.getBusinessLineByAccountType(mainOrderData.getAccountType());
            if (Objects.isNull(businessLineConfig) || !businessLineConfig.isEnabled()) {
                log.info("Business line not found or disabled for account type: {}", mainOrderData.getAccountType());
                return;
            }
            QuotedTypeConfig quotedTypeConfig = businessLineConfig.getQuotedTypeConfig(mainOrderData.getQuotedType());
            // 检查所有必需的流水类型是否都存在
            boolean allFlowOrdersExist = true;
            for (String bizType : quotedTypeConfig.getOutBizTypes()) {
                String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
                Boolean exists = reconRedisTemplate.hasKey(flowOrderKey);
                if (Boolean.FALSE.equals(exists)) {
                    allFlowOrdersExist = false;
                    log.info("Flow order not found for bizType: {}, orderId: {}", bizType, orderId);
                    break;
                }
            }
            for (String bizType : quotedTypeConfig.getInBizTypes()) {
                String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
                Boolean exists = reconRedisTemplate.hasKey(flowOrderKey);
                if (Boolean.FALSE.equals(exists)) {
                    allFlowOrdersExist = false;
                    log.info("Flow order not found for bizType: {}, orderId: {}", bizType, orderId);
                    break;
                }
            }
            // 如果所有需要的流水订单都存在，则进行匹配处理
            if (allFlowOrdersExist) {
                // 获取或创建该订单的锁
                Lock orderLock = orderLocks.computeIfAbsent(orderId, k -> new ReentrantLock());
                // 尝试获取锁，如果获取不到，直接返回
                if (!orderLock.tryLock()) {
                    log.info("Order {} is already being processed by another thread, skipping", orderId);
                    return;
                }
                try {
                    // 创建组合订单对象
                    CombinedOrderData combinedOrderData = new CombinedOrderData();
                    combinedOrderData.addMainOrder(mainOrderData);

                    for (String bizType : quotedTypeConfig.getOutBizTypes()) {
                        String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
                        Object bill = reconRedisTemplate.opsForValue().get(flowOrderKey);
                        if (Objects.isNull(bill)) {
                            log.error("flow order not found for bizType: {}, orderId: {}", bizType, orderId);
                        }
                        FlowOrder flowOrderData = JSON.parseObject(bill.toString(), FlowOrder.class);
                        combinedOrderData.addFlowOrder(bizType, flowOrderData);
                    }
                    for (String bizType : quotedTypeConfig.getInBizTypes()) {
                        String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
                        Object bill = reconRedisTemplate.opsForValue().get(flowOrderKey);
                        if (Objects.isNull(bill)) {
                            log.error("flow order not found for bizType: {}, orderId: {}", bizType, orderId);
                        }
                        FlowOrder flowOrderData = JSON.parseObject(bill.toString(), FlowOrder.class);
                        combinedOrderData.addFlowOrder(bizType, flowOrderData);
                    }
                    BusinessLineEnum businessLineEnum = BusinessLineEnum.getByCode(businessLineConfig.getCode());
                    BusinessLineStrategy strategy = businessLineStrategyFactory.getStrategy(businessLineEnum);
                    ReconOrderResult orderResult = strategy.processCombinedOrder(combinedOrderData, businessLineConfig);
                    if (orderResult.isSuccess()) {
                        strategy.cleanupOrderData(combinedOrderData);
                        log.info("Successfully matched and processed order: {}", orderId);
                    } else {
                        // 对账失败，记录失败信息
                        recordFailure(mainOrderData, orderResult);
                        strategy.cleanupOrderData(combinedOrderData);
                    }

                } finally {
                    // 处理完成后释放锁
                    orderLock.unlock();
                    // 清理锁对象
                    if (orderLock instanceof ReentrantLock && !((ReentrantLock) orderLock).hasQueuedThreads()) {
                        orderLocks.remove(orderId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to process business line: {}", orderId, e);
        }
    }

    /**
     * 记录失败信息
     *
     * @param mainOrderData 订单数据
     * @param orderResult   对账结果
     */
    private void recordFailure(ConvertOrder mainOrderData, ReconOrderResult orderResult) {

        ReconOrderFailureRecord failureRecord = ReconOrderFailureRecord.builder()
                .bizId(mainOrderData.getBizOrderId())
                .userId(mainOrderData.getUserId())
                .orderId(mainOrderData.getBizOrderId())
                .orderType(ReconOrderTypeEnum.CONVERT.getCode())
                .failureType(orderResult.getFailureType().toString())
                .failureReason(orderResult.getFailureType().getMessage())
                .status(ReconOrderStausEnum.RECONCILED_FAILURE.getCode()) // 对账失败
                .rawData(JSON.toJSONString(mainOrderData))
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        reconOrderFailureRecordService.recordFailure(failureRecord);
    }

    // 延迟处理队列
    private final DelayQueue<DelayedFlowOrder> delayedFlowQueue = new DelayQueue<>();
    private final ScheduledExecutorService retryExecutor = Executors.newScheduledThreadPool(2);


    /**
     * 延迟流水订单包装类
     */
    private static class DelayedFlowOrder implements Delayed {
        private final FlowOrder flowOrder;
        private final ReconOrderConfig config;
        private final long executeTime;
        private final int retryCount;
        private final String originalOrderId;

        public DelayedFlowOrder(FlowOrder flowOrder, ReconOrderConfig config,
                                long delayMs, int retryCount, String originalOrderId) {
            this.flowOrder = flowOrder;
            this.config = config;
            this.executeTime = System.currentTimeMillis() + delayMs;
            this.retryCount = retryCount;
            this.originalOrderId = originalOrderId;
        }

        @Override
        public long getDelay(TimeUnit unit) {
            return unit.convert(executeTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
        }

        @Override
        public int compareTo(Delayed o) {
            return Long.compare(this.executeTime, ((DelayedFlowOrder) o).executeTime);
        }
    }


/**
 * 保存流水订单到Redis
 *
 * @param flowOrder 流水订单数据
 * @param config    配置信息
 * @param orderId   订单ID（可能是转换后的主订单ID）
 */
private void saveFlowOrderToRedis(FlowOrder flowOrder, ReconOrderConfig config, String orderId) {
    try {
        String bizType = flowOrder.getBizType();
        String key = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
        String value = JSON.toJSONString(flowOrder);
        int expireTime = config.getOrderExpireTime();
        reconRedisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.MINUTES);

        log.info("Saved flow order to Redis: flow={}, orderId={}, key={}",
                bizType, orderId, key);

    } catch (Exception e) {
        log.error("Failed to save flow order to Redis: bizType={}, orderId={}",
                flowOrder.getBizType(), orderId, e);
        throw e;
    }
}

/**
 * 计算重试延迟时间（指数退避）
 */
private static long calculateRetryDelay(int retryCount,ReconOrderConfig config) {
    long delay = config.getInitialDelayMs() * (1L << retryCount); // 指数退避：1s, 2s, 4s, 8s, 16s
    return Math.min(config.getMaxDelayMs(), delay);
}



/**
 * 启动延迟队列处理线程
 */
@PostConstruct
public void startDelayedFlowProcessor() {
    retryExecutor.submit(() -> {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                DelayedFlowOrder delayedOrder = delayedFlowQueue.take();

                log.info("Processing delayed margin flow, orderId: {}, retryCount: {}",
                        delayedOrder.originalOrderId, delayedOrder.retryCount);
                // 重新尝试处理
                processDelayedFlow(delayedOrder);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("Delayed flow processor interrupted");
                break;
            } catch (Exception e) {
                log.error("Error in delayed flow processor", e);
            }
        }
    });
}

/**
 * 处理延迟的流水订单
 */
private void processDelayedFlow(DelayedFlowOrder delayedOrder) {
    try {
        String bizType = delayedOrder.flowOrder.getBizType();
        String originalOrderId = delayedOrder.originalOrderId;
        int retryCount = delayedOrder.retryCount;
        String finalOrderId;
        if (delayedOrder.config.getNeedConvertBizTypes().contains(bizType)) {
            // 流水重试
            finalOrderId = retryFlowConversion(delayedOrder.flowOrder, delayedOrder.config, originalOrderId, retryCount);
        } else {
            finalOrderId = originalOrderId;
        }
        if (finalOrderId != null) {
            // 转换成功，保存并尝试匹配
            saveFlowOrderToRedis(delayedOrder.flowOrder, delayedOrder.config, finalOrderId);
            tryMatchOrderFlow(finalOrderId, delayedOrder.config);
        }

    } catch (Exception e) {
        log.error("Error processing delayed flow order: {}", delayedOrder.originalOrderId, e);
    }
}

/**
 * 重试流水转换
 *
 * @return 转换后的订单ID，如果返回null表示已加入延迟队列，不应继续处理
 */
private String retryFlowConversion(FlowOrder flowOrder, ReconOrderConfig config, String originalOrderId, int retryCount) {
    try {
        String mainOrderId;
        // 1. 检查是否可能是直接匹配的情况
        if (isMainOrderExists(originalOrderId)) {
            log.info("Margin flow direct match succeeded on retry #{}: {}", retryCount, originalOrderId);
            return originalOrderId;
        }
        // 2. 尝试转换订单ID
        mainOrderId = convertFlowToMainOrderId(originalOrderId);
        if (!mainOrderId.equals(originalOrderId)) {
            // 转换成功
            log.info("Margin flow conversion succeeded on retry #{}: {} -> {}", retryCount, originalOrderId, mainOrderId);
            return mainOrderId;
        }
        // 3. 转换失败，检查是否需要继续重试
        if (retryCount < config.getRetryCount()) {
            long delayMs = calculateRetryDelay(retryCount,config);
            DelayedFlowOrder delayedOrder = new DelayedFlowOrder(
                    flowOrder, config, delayMs, retryCount + 1, originalOrderId);
            delayedFlowQueue.offer(delayedOrder);
            log.info("Margin flow conversion failed, scheduled for retry #{} after {}ms. OrderId: {}",
                    retryCount + 1, delayMs, originalOrderId);
            return null; // 返回null表示已加入延迟队列
        } else {
            // 超过最大重试次数，记录失败并使用原始ID
            log.error("Margin flow conversion failed after {} retries, using original ID. OrderId: {}",
                    config.getRetryCount(), originalOrderId);
            recordConversionFailure(flowOrder, "超过最大重试次数:" + retryCount);
            return originalOrderId;
        }
    } catch (Exception e) {
        log.error("Error processing margin flow retry, orderId: {}, retryCount: {}",
                originalOrderId, retryCount, e);
        if (retryCount < config.getRetryCount()) {
            // 异常情况也进行重试
            long delayMs = calculateRetryDelay(retryCount,config);
            DelayedFlowOrder delayedOrder = new DelayedFlowOrder(
                    flowOrder, config, delayMs, retryCount + 1, originalOrderId);
            delayedFlowQueue.offer(delayedOrder);
            log.info("Margin flow exception occurred, scheduled for retry #{} after {}ms. OrderId: {}",
                    retryCount + 1, delayMs, originalOrderId);
            return null; // 返回null表示已加入延迟队列
        } else {
            recordConversionFailure(flowOrder, "Exception after max retries: " + e.getMessage());
            return originalOrderId;
        }
    }
}

/**
 * 把订单流水转换为主订单ID，转换链路：流水订单 -> 划转订单 -> 操盘订单 -> 主订单
 *
 * @param flowBizId 流水bizId
 * @return
 */
private String convertFlowToMainOrderId(String flowBizId) {

    return reconRedisTemplate.execute((RedisCallback<String>) connection -> {
        try {
            // 1. 检查划转单是否存在
            String transferKey = TRANS_ORDER_PREFIX + flowBizId;
            byte[] transferData = connection.get(transferKey.getBytes());

            if (transferData == null) {
                log.warn("Transfer order not found for margin flow: {}", flowBizId);
                return flowBizId; // 返回原始ID表示转换失败
            }
            // 2. 解析划转单获取操盘单ID
            String transferJson = new String(transferData);
            JSONObject transferObj = JSON.parseObject(transferJson);
            String hedgingOrderId = transferObj.getString("bizOrderId");
            if (StringUtils.isEmpty(hedgingOrderId)) {
                log.warn("Invalid transfer order data, missing bizOrderId: {}", transferJson);
                return flowBizId;
            }
            // 3. 检查操盘单是否存在
            String hedgingKey = SPOT_HEDGING_ORDER_PREFIX + hedgingOrderId;
            byte[] hedgingData = connection.get(hedgingKey.getBytes());
            if (hedgingData == null) {
                log.warn("Hedging order not found: {}", hedgingOrderId);
                return flowBizId;
            }
            // 4. 解析操盘单获取主订单ID
            String hedgingJson = new String(hedgingData);
            JSONObject hedgingObj = JSON.parseObject(hedgingJson);
            String mainOrderId = hedgingObj.getString("bizOrderId");

            if (StringUtils.isEmpty(mainOrderId)) {
                log.warn("Invalid hedging order data, missing bizOrderId: {}", hedgingJson);
                return flowBizId;
            }
            // 5. 检查主订单是否存在
            String mainOrderKey = MAIN_ORDER_PREFIX + mainOrderId;
            Boolean mainOrderExists = connection.exists(mainOrderKey.getBytes());

            if (!Boolean.TRUE.equals(mainOrderExists)) {
                log.warn("Main order not found: {}", mainOrderId);
                return flowBizId;
            }

            log.info("Successfully converted margin flow: {} -> {}", flowBizId, mainOrderId);
            return mainOrderId;

        } catch (Exception e) {
            log.error("Error converting margin flow ID: {}", flowBizId, e);
            return flowBizId;
        }
    });
}


/**
 * 检查主订单是否存在
 */
private boolean isMainOrderExists(String orderId) {
    try {
        String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
        Boolean exists = reconRedisTemplate.hasKey(mainOrderKey);
        return Boolean.TRUE.equals(exists);
    } catch (Exception e) {
        log.error("Error checking main order existence for orderId: {}", orderId, e);
        return false;
    }
}

/**
 * 记录转换失败
 */
private void recordConversionFailure(FlowOrder flowOrder, String reason) {
    try {
        // 可以记录到数据库或发送告警
        log.error("Margin flow conversion failed permanently. OrderId: {}, BizType: {}, Reason: {}",
                flowOrder.getOrderId(), flowOrder.getBizType(), reason);

        alarmNotifyService.alarm(AlarmTemplateEnum.CHECK_RECON_ORDER_ERROR,
                ReconOrderTypeEnum.CONVERT.getDesc(),
                flowOrder.getOrderId(),
                reason);
    } catch (Exception e) {
        log.error("Failed to record conversion failure", e);
    }
}

@PreDestroy
public void shutdown() {
    retryExecutor.shutdown();
    try {
        if (!retryExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
            retryExecutor.shutdownNow();
        }
    } catch (InterruptedException e) {
        retryExecutor.shutdownNow();
        Thread.currentThread().interrupt();
    }
}

/**
 * 新增主订单索引保存的方法
 */
public void saveMainOrderIndex(String orderId, ReconOrderConfig reconOrderConfig) {

    // todo 主订单才能调用
    String shardKey = getDynamicShardKey(reconOrderConfig);
    reconRedisTemplate.opsForZSet().add(shardKey, orderId, (double) System.currentTimeMillis() / 1000);
    if (reconOrderConfig.isDynamicShardingEnabled()) {
        // 设置分片过期时间
        reconRedisTemplate.expire(shardKey, reconOrderConfig.getOrderExpireTime() + 10, TimeUnit.MINUTES);
    }
    log.info("Saved main order index for shard key: {}", shardKey);
}

private String getDynamicShardKey(ReconOrderConfig reconOrderConfig) {
    if (reconOrderConfig.isDynamicShardingEnabled()) {
        LocalDateTime now = LocalDateTime.now();
        return MAIN_ORDER_TIMESTAMP_PREFIX + now.format(SHARD_FORMATTER);
    }
    return DEFAULT_MAIN_ORDER_INDEX_KEY;
}

}
