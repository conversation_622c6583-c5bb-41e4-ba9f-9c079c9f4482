package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.AssetsBillConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("assetsBillConfigMapper")
public interface AssetsBillConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(AssetsBillConfig record);

    AssetsBillConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AssetsBillConfig record);

    /**
     * 查询所有对账
     *
     * @return
     */
    List<AssetsBillConfig> listAssetsBillConfig();


    /**
     * 根据类型和参数查询对账配置
     *
     * @param assetsCheckType
     * @param assetsCheckParam
     * @return
     */
    AssetsBillConfig selectByTypeAndParam(@Param("assetsCheckType") String assetsCheckType, @Param("assetsCheckParam") String assetsCheckParam);

    AssetsBillConfig selectMinCheckOkTime();

    /**
     * 重置对账位置和时间
     *
     * @param syncPos
     * @param resetCheckTime
     * @param assetsCheckType
     * @param assetsCheckParam
     * @return
     */
    int updateSyncPosAndTime(@Param("syncPos") Long syncPos, @Param("resetCheckTime") Date resetCheckTime,
                             @Param("assetsCheckType") String assetsCheckType, @Param("assetsCheckParam") String assetsCheckParam);

    /**
     * 删除数据
     *
     * @param assetsCheckType
     * @param assetsCheckParam
     */
    int deleteAll(@Param("assetsCheckType") String assetsCheckType, @Param("assetsCheckParam") String assetsCheckParam);
}