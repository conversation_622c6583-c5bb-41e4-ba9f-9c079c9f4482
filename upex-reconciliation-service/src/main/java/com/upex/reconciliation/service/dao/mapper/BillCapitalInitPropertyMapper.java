package com.upex.reconciliation.service.dao.mapper;


import com.upex.reconciliation.service.dao.entity.BillCapitalInitProperty;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/27 下午3:02
 * @Description
 */
@Repository("billCapitalInitPropertyMapper")
public interface BillCapitalInitPropertyMapper {
    /**
     * 获取业务线初始化数据
     *
     * @param businessType
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillCapitalInitProperty> selectRecords(
            @Param("accountType") String accountType,
            @Param("accountParam") String accountParam,
            @Param("businessType") Integer businessType);

    /**
     * 获取业务线初始化数据
     *
     * @param businessType
     * @return
     */
    List<BillCapitalInitProperty> selectRecordsByBusinessType(
            @Param("businessType") Integer businessType);

    /**
     * 批量插入
     *
     * @param records
     * @return
     */
    int batchInsert(@Param("records") List<BillCapitalInitProperty> records);

    /**
     * 删除数据
     *
     * @param id
     * @return
     */
    int deleteById(@Param("id") Long id);

    boolean deleteByCheckTime(@Param("accountType") Byte accountType,
                              @Param("accountParam") String accountParam,
                              @Param("checkTime") Date checkTime,
                              @Param("businessType") Integer businessType);

    boolean deleteByBusinessType(@Param("accountType") Byte accountType,
                                 @Param("accountParam") String accountParam,
                                 @Param("businessType") Integer businessType);
}
