package com.upex.reconciliation.service.business.convert.model;


import lombok.Data;

@Data
public class TransOrder extends MainOrder {

    private Long id;

    /**
     * NORMAL_TRANSFER(Code:1, desc:"普通划转"),
     * SMALL_TRANSFER(code:2,desc:"小额划转"),
     * FINAL_TRANSFER(Code:3, desc:"终态划转"),
     * LARGE_TRANSFER(code:4,desc:"大额划转"),
     * LARGE_BACK_TRANSFER(Code: 5, desc:"大额划转回退"),
     * LARGE_BACK_LAST_TRANSFER( code: 6, desc:"大额划转回退最后一笔"),
     * SYS_FINAL_TRANSFER(Code:7, desc:"系统操盘终态划划转"),
     * SYS_CONTRACT_FINAL_TRANSFER( code: 8, desc:"合约系统操盘终态划转"),
     * SYS_MARGIN_FINAL_TRANSFER( code: 9, desc:"杠杆系系统操盘终态划转")
     * SYS UTA FINAL TRANSFER( code: 10. (desc:"统一账户系统操盘终态划转")
     */
    private Integer transSource;
    
}
