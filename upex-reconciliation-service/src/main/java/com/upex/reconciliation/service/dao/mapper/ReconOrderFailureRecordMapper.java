package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 对账失败记录Mapper接口
 */
@Repository
public interface ReconOrderFailureRecordMapper {
    
    /**
     * 插入失败记录
     * 
     * @param record 失败记录
     * @return 影响行数
     */
    int insert(ReconOrderFailureRecord record);
    
    /**
     * 批量插入失败记录
     * 
     * @param records 失败记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<ReconOrderFailureRecord> records);
    
    /**
     * 根据ID查询失败记录
     * 
     * @param id 记录ID
     * @return 失败记录
     */
    ReconOrderFailureRecord selectById(@Param("id") Long id);
    
    /**
     * 根据业务ID查询失败记录
     * 
     * @param bizId 业务ID
     * @return 失败记录
     */
    ReconOrderFailureRecord selectByBizId(@Param("bizId") Long bizId);
    
    /**
     * 根据订单ID查询失败记录
     * 
     * @param orderId 订单ID
     * @return 失败记录
     */
    ReconOrderFailureRecord selectByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 根据账户ID查询失败记录
     * 
     * @param userId 账户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 失败记录列表
     */
    List<ReconOrderFailureRecord> selectByUserId(@Param("userId") Long userId,
                                                 @Param("startTime") Date startTime,
                                                 @Param("endTime") Date endTime);
    
    /**
     * 更新失败记录状态
     * 
     * @param id 记录ID
     * @param status 状态
     * @param processUser 处理人
     * @param processRemark 处理备注
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, 
                     @Param("status") Byte status, 
                     @Param("processUser") String processUser, 
                     @Param("processRemark") String processRemark);

    /**
     * 更新失败记录的重试次数
     *
     * @param id 记录ID
     * @param retryCount 重试次数
     * @return 影响行数
     */
    int updateRetryCount(@Param("id") Long id, @Param("retryCount") Integer retryCount);
    
    /**
     * 根据业务类型和失败类型查询未处理的失败记录
     * 
     * @param orderType 业务类型
     * @param failureType 失败类型
     * @return 失败记录列表
     */
    List<ReconOrderFailureRecord> selectUnprocessed(@Param("orderType") Byte orderType,
                                                   @Param("failureType") Byte failureType);
}