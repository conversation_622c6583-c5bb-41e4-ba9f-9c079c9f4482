package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillFlowCheckConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("billFlowCheckConfigMapper")
public interface BillFlowCheckConfigMapper {

    int deleteByPrimaryKey(Long id);

    BillFlowCheckConfig selectByPrimaryKey(Long id);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("records") List<BillFlowCheckConfig> list);

    /**
     * 修改
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(BillFlowCheckConfig record);

    /**
     * 根据类型及业务线查询对应流水检测配置
     *
     * @param type
     * @param accountType
     * @return
     */
    List<BillFlowCheckConfig> getBillConfigList(@Param("type") Integer type, @Param("accountType") String accountType);
}