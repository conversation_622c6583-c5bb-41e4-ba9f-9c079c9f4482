package com.upex.reconciliation.service.model.dto;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * user_id coin_id id
 */
@Data
public class UserCoinIdDTO extends AbstractSProperty{
    private Long id;
    private Long userId;
    private Integer coinId;
    private BigDecimal prop1 = BigDecimal.ZERO;
    private BigDecimal prop2 = BigDecimal.ZERO;
    private BigDecimal prop3 = BigDecimal.ZERO;
    private BigDecimal prop4 = BigDecimal.ZERO;
    private BigDecimal prop5 = BigDecimal.ZERO;
    private BigDecimal prop6 = BigDecimal.ZERO;
    private BigDecimal prop7 = BigDecimal.ZERO;
    private BigDecimal prop8 = BigDecimal.ZERO;
    private Date checkTime;
    public String groupByCoinUser() {
        return coinId + "#" + userId;
    }
}
