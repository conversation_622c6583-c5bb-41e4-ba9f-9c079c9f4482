package com.upex.reconciliation.service.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 总账 多空仓+盈亏 聚合属性
 * prop1 多仓数量 prop2 空仓数量 prop3 已实现 prop4 未实现 prop5 已实现初始值 prop6 mprice prop7 重算已实现 prop8 重算未实现
 */
@Data
public class SymbolCheckProperty extends AbstractProperty {
    /***交易对标识id***/
    private String symbolId;
    /***检查时间***/
    private Date checkTime;
    private Byte accountType;
    private String accountParam;
    /***是否存在负值仓位***/
    private Boolean existNegativePosition = false;
}
