package com.upex.reconciliation.service.dao.bill.entity;

import com.upex.reconciliation.service.common.constants.BillConstants;
import lombok.Data;

import java.util.Date;

@Data
public class AssetsBillConfig {

    private Long id;

    private String assetsCheckType;

    private String assetsCheckParam;

    private Long syncPos;

    private Date checkOkTime;

    private Byte status;

    private Date createTime;

    private Date updateTime;

    /**
     * 是否修改对账时间
     */
    private Boolean flag;
    /**
     * 执行优先级
     */
    private Integer priorityLevel;

    /**
     * 获取检查唯一key
     *
     * @return
     */
    public String getCheckUniqKey() {
        return this.assetsCheckType + BillConstants.SEPARATOR + this.assetsCheckParam;
    }
}