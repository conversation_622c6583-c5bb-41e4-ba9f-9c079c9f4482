package com.upex.reconciliation.service.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PositionStatisticsDTO {
    /***多仓***/
    private BigDecimal lCount = BigDecimal.ZERO;
    /***空仓***/
    private BigDecimal sCount = BigDecimal.ZERO;
    /***未实现***/
    private BigDecimal unRealized = BigDecimal.ZERO;
    /***重算未实现***/
    private BigDecimal reUnRealized = BigDecimal.ZERO;
    /***保证金币未实现***/
    private BigDecimal marginUnRealized = BigDecimal.ZERO;
    /***重算保证金币未实现***/
    private BigDecimal marginReUnRealized = BigDecimal.ZERO;
    /***保证金币种总未实现***/
    private BigDecimal totalMarginUnRealized = BigDecimal.ZERO;
    /***是否存在负值仓位***/
    private Boolean existNegativePosition = false;
}
