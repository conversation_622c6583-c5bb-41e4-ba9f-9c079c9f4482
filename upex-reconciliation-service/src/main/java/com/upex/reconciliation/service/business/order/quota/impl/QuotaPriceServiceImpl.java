package com.upex.reconciliation.service.business.order.quota.impl;

import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.convert.model.ExchangePriceValidationParams;
import com.upex.reconciliation.service.business.convert.model.KLineDataDTO;
import com.upex.reconciliation.service.business.convert.model.PriceValidationResult;
import com.upex.reconciliation.service.business.order.remote.KlineInnerService;
import com.upex.reconciliation.service.business.order.quota.QuotaPriceService;
import com.upex.reconciliation.service.common.constants.enums.CoinIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 行情价格计算服务实现
 */
@Slf4j
@Service
public class QuotaPriceServiceImpl implements QuotaPriceService {

    @Resource
    private KlineInnerService klineInnerService;

    @Resource
    private CommonService commonService;

    @Override
    public PriceValidationResult validatePrice(BigDecimal tradePrice, KLineDataDTO kLineData,
                                               BigDecimal highThreshold, BigDecimal lowThreshold) {
        if (kLineData == null) {
            return PriceValidationResult.failure(tradePrice, null, null, null, null, "K线数据为空");
        }

        BigDecimal marketLow = kLineData.getLow();
        BigDecimal marketHigh = kLineData.getHigh();

        // 计算允许的市场价格范围
        BigDecimal allowedLow = marketLow.multiply(BigDecimal.ONE.subtract(lowThreshold));
        BigDecimal allowedHigh = marketHigh.multiply(BigDecimal.ONE.add(highThreshold));

        // 判断是否在允许范围内
        boolean isValid = tradePrice.compareTo(allowedLow) >= 0 && tradePrice.compareTo(allowedHigh) <= 0;

        if (isValid) {
            return PriceValidationResult.success(tradePrice, marketLow, marketHigh, allowedLow, allowedHigh);
        } else {
            String errorMessage = String.format("成交价格超出指数价格阈值范围. 利润价: %s, K线最高价: %s, K线最低价: %s, 允许最低价: %s, 允许最高价: %s",
                    tradePrice, marketHigh, marketLow, allowedLow, allowedHigh);
            return PriceValidationResult.failure(tradePrice, marketLow, marketHigh, allowedLow, allowedHigh, errorMessage);
        }
    }


    @Override
    public PriceValidationResult validateTrianglePrice(BigDecimal tradePrice, KLineDataDTO toCoinKLineData,
                                                       KLineDataDTO fromCoinKLineData,
                                                       BigDecimal highThreshold, BigDecimal lowThreshold) {
        if (toCoinKLineData == null || fromCoinKLineData == null) {
            return PriceValidationResult.failure(tradePrice, null, null, null, null, "K线数据为空");
        }

        // 计算市场价格范围
        BigDecimal toLowUsd = toCoinKLineData.getLow();
        BigDecimal toHighUsd = toCoinKLineData.getHigh();
        BigDecimal fromLowUsd = fromCoinKLineData.getLow();
        BigDecimal fromHighUsd = fromCoinKLineData.getHigh();

        // 计算市场价格范围
        BigDecimal lowMarket = toLowUsd.divide(fromHighUsd, 18, RoundingMode.HALF_UP);
        BigDecimal highMarket = toHighUsd.divide(fromLowUsd, 18, RoundingMode.HALF_UP);

        // 基于相对阈值的
        BigDecimal allowedLow = lowMarket.multiply(BigDecimal.ONE.subtract(lowThreshold));
        BigDecimal allowedHigh = highMarket.multiply(BigDecimal.ONE.add(highThreshold));

        // 判断是否在允许范围内
        boolean isValid = tradePrice.compareTo(allowedLow) >= 0 && tradePrice.compareTo(allowedHigh) <= 0;

        if (isValid) {
            return PriceValidationResult.success(tradePrice, lowMarket, highMarket, allowedLow, allowedHigh);
        } else {
            log.error("valid triangle price failed, tradePrice: {}, lowMarket: {}, highMarket: {}, allowedLow: {}, allowedHigh: {}",
                    tradePrice, lowMarket, highMarket, allowedLow, allowedHigh);
            String errorMessage = String.format("三角兑换成交价格超出指数价格阈值范围. 利润价: %s, 市场最高价: %s, 市场最低价: %s, 允许最低价: %s, 允许最高价: %s",
                    tradePrice, highMarket, lowMarket, allowedLow, allowedHigh);
            return PriceValidationResult.failure(tradePrice, lowMarket, highMarket, allowedLow, allowedHigh, errorMessage);
        }
    }


    @Override
    public PriceValidationResult validateExchangePrice(Integer fromCoinId, BigDecimal fromCoinCount,
                                                       Integer toCoinId, BigDecimal toCoinCount,
                                                       long timestamp, BigDecimal highThreshold, BigDecimal lowThreshold) {

        //  判断是否为USDT兑换
        boolean isUsdtExchange = isUsdtExchange(fromCoinId, toCoinId);
        if (isUsdtExchange) {
            BigDecimal tradePrice;
            Integer coinId;
            if (isUsdtBuy(fromCoinId)) {
                tradePrice = fromCoinCount.divide(toCoinCount, 18, RoundingMode.HALF_UP);
                coinId = toCoinId;
            } else {
                tradePrice = toCoinCount.divide(fromCoinCount, 18, RoundingMode.HALF_UP);
                coinId = fromCoinId;
            }
            return validateUsdtExchange(coinId, tradePrice, timestamp, highThreshold, lowThreshold);
        } else {
            // 计算成交价
            BigDecimal tradePrice = fromCoinCount.divide(toCoinCount, 18, RoundingMode.HALF_UP);
            // 三角兑换
            return validateTriangleExchange(toCoinId, fromCoinId, tradePrice, timestamp, highThreshold, lowThreshold);
        }
    }
    @Override
    public PriceValidationResult validateUsdtExchange(ExchangePriceValidationParams params) {

        //  判断是否为USDT兑换
        boolean isUsdtExchange = isUsdtExchange(params.getFromCoinId(), params.getToCoinId());
        if (isUsdtExchange) {
            BigDecimal tradePrice;
            Integer coinId;
            if (isUsdtBuy(params.getFromCoinId())) {
                tradePrice = params.getFromCoinCount().divide(params.getToCoinCount(), 18, RoundingMode.HALF_UP);
                coinId = params.getToCoinId();
            } else {
                tradePrice = params.getToCoinCount().divide(params.getFromCoinCount(), 18, RoundingMode.HALF_UP);
                coinId = params.getFromCoinId();
            }
            return validateUsdtExchange(coinId, tradePrice, params.getTimestamp(), params.getHighThreshold(), params.getLowThreshold());
        } else {
            // 计算成交价
            BigDecimal tradePrice = params.getFromCoinCount().divide(params.getToCoinCount(), 18, RoundingMode.HALF_UP);
            // 三角兑换
            return validateTriangleExchange(params.getToCoinId(), params.getFromCoinId(), tradePrice, params.getTimestamp(), params.getHighThreshold(), params.getLowThreshold());
        }
    }

    /**
     * 判断是否为USDT兑换
     */
    private boolean isUsdtExchange(Integer fromCoinId, Integer toCoinId) {
        // 判断fromCoinId是否为USDT
        return CoinIdEnum.USDT_UPCASE.getCode().equals(fromCoinId)
                || CoinIdEnum.USDT_UPCASE.getCode().equals(toCoinId);
    }

    /**
     * 判断是否为USDT买入交易
     * 买入: fromCoinId是USDT, toCoinId是其他币种
     */
    private boolean isUsdtBuy(Integer fromCoinId) {
        return CoinIdEnum.USDT_UPCASE.getCode().equals(fromCoinId);
    }


    /**
     * 验证USDT兑换价格
     */
    public PriceValidationResult validateUsdtExchange(Integer coinId, BigDecimal tradePrice,
                                                       long timestamp, BigDecimal highThreshold, BigDecimal lowThreshold) {

        if (CoinIdEnum.USDT_UPCASE.getCode().equals(coinId)) {
            return PriceValidationResult.failure(tradePrice, null, null, null, null,
                    String.format("USDT无法进行价格验证, coinId: %s", coinId));
        }
        // 获取币种名称
        String toCoinName = commonService.getCoinName(coinId);
        if (toCoinName == null) {
            return PriceValidationResult.failure(tradePrice, null, null, null, null,
                    String.format("无法获取币种名称, coinId: %s", coinId));
        }
        // 构建交易对名称
        String pairName = toCoinName+ CoinIdEnum.USDT_UPCASE.getDesc();
        // 获取K线数据
        KLineDataDTO kLineData = klineInnerService.getSpotKline1mIndex(pairName, timestamp);
        if (kLineData == null) {
            return PriceValidationResult.failure(tradePrice, null, null, null, null,
                    String.format("获取K线数据失败, pairName: %s, timestamp: %s", pairName, timestamp));
        }
        // 验证价格
        return validatePrice(tradePrice, kLineData, highThreshold, lowThreshold);
    }

    /**
     * 验证三角兑换价格
     */
    private PriceValidationResult validateTriangleExchange(Integer toCoinId, Integer fromCoinId, BigDecimal tradePrice,
                                                           long timestamp, BigDecimal highThreshold, BigDecimal lowThreshold) {
        // 获取币种名称
        String toCoinName = commonService.getCoinName(toCoinId);
        String fromCoinName = commonService.getCoinName(fromCoinId);

        if (toCoinName == null || fromCoinName == null) {
            return PriceValidationResult.failure(tradePrice, null, null, null, null,
                    String.format("无法获取币种名称, toCoinId: %s, fromCoinId: %s", toCoinId, fromCoinId));
        }

        // 构建交易对名称
        String toPairName = toCoinName + CoinIdEnum.USDT_UPCASE.getDesc();
        String fromPairName = fromCoinName + CoinIdEnum.USDT_UPCASE.getDesc();

        // 获取K线数据
        KLineDataDTO toCoinKLineData = klineInnerService.getSpotKline1mIndex(toPairName, timestamp);
        KLineDataDTO fromCoinKLineData = klineInnerService.getSpotKline1mIndex(fromPairName, timestamp);

        if (toCoinKLineData == null || fromCoinKLineData == null) {
            return PriceValidationResult.failure(tradePrice, null, null, null, null,
                    String.format("获取K线数据失败, toPairName: %s, fromPairName: %s, timestamp: %s",
                            toPairName, fromPairName, timestamp));
        }
        // 验证三角兑换价格
        return validateTrianglePrice(tradePrice, toCoinKLineData, fromCoinKLineData, highThreshold, lowThreshold);
    }
}
