package com.upex.reconciliation.service.business.order.convert.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.service.business.convert.ReconOrderResult;
import com.upex.reconciliation.service.business.convert.config.BusinessLineConfig;
import com.upex.reconciliation.service.business.convert.enums.BusinessLineEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderFailureEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.*;
import com.upex.reconciliation.service.business.order.convert.strategy.AbstractConvertBusinessLineStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.CHECK_CONVERT_RECON_ORDER_ERROR;

/**
 * 用户闪兑策略实现
 */
@Component
@Slf4j
public class UserConvertStrategy extends AbstractConvertBusinessLineStrategy {


    @Override
    public BusinessLineEnum getSupportedBusinessLine() {
        return BusinessLineEnum.USER_CONVERT_SPOT;
    }


    public ReconOrderResult validatePriceData(CombinedOrderData combinedOrderData, BusinessLineConfig config) {

        ConvertOrder mainOrder = (ConvertOrder) combinedOrderData.getMainOrder();
        // 用户盘口闪兑忽略价格计算校验
        if (mainOrder.getQuotedType().equals(1)) {
            if (Objects.isNull(mainOrder.getPt1())
                    && Objects.isNull(mainOrder.getPt2())) {
                log.error("Price data is missing for order: {}", JSON.toJSONString(combinedOrderData));
                String errorMessage = "价格数据缺失, pt1或pt2为空";
                alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                        ReconOrderTypeEnum.CONVERT.getDesc(),
                        mainOrder.getBizOrderId(),
                        mainOrder.getUserId(),
                        mainOrder.getSystemUserId(),
                        mainOrder.getFromCoinName() + "(" + mainOrder.getFromCoinId() + ")",
                        mainOrder.getToCoinName() + "(" + mainOrder.getToCoinId() + ")",
                        errorMessage);
                return ReconOrderResult.failure(ReconOrderFailureEnum.PRICE_DATA_MISSING, errorMessage);
            }
            // 判断是否为USDT交易对
            boolean isUsdt = mainOrder.isUsdt();
            // 利润价格
            BigDecimal profitPrice = isUsdt ? mainOrder.getPt1() : mainOrder.getPt2();
            // 成本价格
            BigDecimal costPrice = isUsdt ? mainOrder.getPc1() : mainOrder.getPc2();
            // 检查利润价格和成本价格的差异是否超过阈值
            String profitCostRate = config.getProfitCostRate();
            boolean isPriceDifferenceExceedsThreshold = profitPrice.subtract(costPrice).abs().divide(costPrice, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal(profitCostRate)) > 0;
            if (isUsdt) {
                // USDT交易对，判断买卖方向
                boolean isBuyDirection = mainOrder.isBuyDirection();
                // 如果是买入方向，利润价格应该大于成本价格；如果是卖出方向，成本价格应该大于利润价格
                if ((isBuyDirection && profitPrice.compareTo(costPrice) < 0 && isPriceDifferenceExceedsThreshold) ||
                        (!isBuyDirection && profitPrice.compareTo(costPrice) > 0 && isPriceDifferenceExceedsThreshold)) {
                    log.error("Price data inconsistency detected for order: {}, profitPrice: {}, costPrice: {}, isBuyDirection: {}",
                            JSON.toJSONString(combinedOrderData), profitPrice, costPrice, isBuyDirection);
                    String errorMessage = String.format("价格数据不一致, 利润价: %s, 成本价: %s, 是否买入: %s", profitPrice, costPrice, isBuyDirection);
                    alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                            ReconOrderTypeEnum.CONVERT.getDesc(),
                            mainOrder.getBizOrderId(),
                            mainOrder.getUserId(),
                            mainOrder.getSystemUserId(),
                            mainOrder.getFromCoinName() + "(" + mainOrder.getFromCoinId() + ")",
                            mainOrder.getToCoinName() + "(" + mainOrder.getToCoinId() + ")",
                            errorMessage);
                    return ReconOrderResult.failure(ReconOrderFailureEnum.INCONSISTENT_PRICE_DATA, errorMessage);

                }
            } else {
                // 非USDT交易对，直接比较利润价格和成本价格
                if (profitPrice.compareTo(costPrice) < 0 && isPriceDifferenceExceedsThreshold) {
                    log.error("Price data inconsistency detected for order: {}, profitPrice: {}, costPrice: {}",
                            JSON.toJSONString(combinedOrderData), profitPrice, costPrice);
                    String errorMessage = String.format("价格数据不一致, 利润价: %s, 成本价: %s", profitPrice, costPrice);
                    alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                            ReconOrderTypeEnum.CONVERT.getDesc(),
                            mainOrder.getBizOrderId(),
                            mainOrder.getUserId(),
                            mainOrder.getSystemUserId(),
                            mainOrder.getFromCoinName() + "(" + mainOrder.getFromCoinId() + ")",
                            mainOrder.getToCoinName() + "(" + mainOrder.getToCoinId() + ")",
                            errorMessage);
                    return ReconOrderResult.failure(ReconOrderFailureEnum.INCONSISTENT_PRICE_DATA, errorMessage);

                }
            }
        }
        return ReconOrderResult.success();
    }

}
