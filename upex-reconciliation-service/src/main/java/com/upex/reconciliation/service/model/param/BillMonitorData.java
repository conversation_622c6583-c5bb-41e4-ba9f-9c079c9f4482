package com.upex.reconciliation.service.model.param;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/16 17:23
 */
@Data
public class BillMonitorData {



    /**
     * 如果需要业务线的话
     */
    private byte accountType;

    private String bizType;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 币种
     */
    private Integer coinId;

    /**
     * 检查时间
     */
    private Date bizTime;


}
